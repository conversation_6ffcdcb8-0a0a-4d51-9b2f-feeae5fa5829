# Migration 317: Fix Event Email Contents Submit Order Structure

## Overview

This migration fixes a data structure issue in the `events` collection where some event documents have an incorrect nested structure for `emailContents.submitOrder`.

## Problem

Some events have `emailContents.submitOrder` with the following incorrect structure:

```javascript
{
    subject: {
        defaultValue: {
            defaultValue: t('emails:event.submitOrder.subject'),
            overrides: [],
        },
        overrides: [],
    },
    introTitle: {
        defaultValue: {
            defaultValue: t('emails:event.submitOrder.introTitle'),
            overrides: [],
        },
        overrides: [],
    },
    contentText: {
        defaultValue: {
            defaultValue: t('emails:event.submitOrder.contentText'),
            overrides: [],
        },
        overrides: [],
    },
}
```

This structure appears to be the module's `EmailWithScenarioOverrides` structure instead of the simple `EventApplicationModuleConfirmEmailContents` structure that events should use.

## Solution

The migration:

1. **Identifies affected events**: Finds events where `emailContents.submitOrder` has the nested `defaultValue.defaultValue` structure
2. **Retrieves correct data**: Looks up each event's associated module via `moduleId` and extracts the correct email contents from `module.emailContents.submitOrder.defaultValue`
3. **Updates events**: Replaces the incorrect nested structure with the correct simple structure from the module
4. **Error handling**: Handles cases where modules don't exist or have incomplete data
5. **Logging**: Provides detailed logging of the migration process and results

## Expected Structure After Migration

Events should have `emailContents.submitOrder` with this correct structure:

```javascript
{
    subject: {
        defaultValue: "Subject text",
        overrides: [],
    },
    introTitle: {
        defaultValue: "Intro title text", 
        overrides: [],
    },
    introImage: { /* optional */ },
    contentText: {
        defaultValue: "Content text",
        overrides: [],
    },
}
```

## Safety Features

- Uses `bulkWrite` with `ordered: false` for efficiency and partial failure tolerance
- Validates replacement data before applying updates
- Provides detailed logging for troubleshooting
- Does not modify events if their associated module is missing or invalid
- Counts and reports successful updates vs errors

## Execution

The migration will run automatically when the application starts and database migrations are executed. It can also be run manually using the migration system.

## Rollback

This migration does not include an automatic rollback. If rollback is needed, the original data structure would need to be restored from backups or reconstructed manually.
