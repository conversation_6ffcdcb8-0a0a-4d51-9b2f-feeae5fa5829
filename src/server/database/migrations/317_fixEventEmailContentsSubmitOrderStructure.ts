/* eslint-disable no-console */
import type { AnyBulkWriteOperation, ObjectId } from 'mongodb';
import { ModuleType } from '../documents';
import type { DatabaseContext } from '../getDatabaseContext';

interface EventWithIncorrectStructure {
    _id: ObjectId;
    moduleId: ObjectId;
    emailContents: {
        submitOrder: {
            subject: {
                defaultValue: {
                    defaultValue: string;
                    overrides: any[];
                };
                overrides: any[];
            };
            introTitle: {
                defaultValue: {
                    defaultValue: string;
                    overrides: any[];
                };
                overrides: any[];
            };
            contentText: {
                defaultValue: {
                    defaultValue: string;
                    overrides: any[];
                };
                overrides: any[];
            };
        };
    };
}

interface EventApplicationModule {
    _id: ObjectId;
    _type: ModuleType.EventApplicationModule;
    emailContents: {
        submitOrder: {
            defaultValue: {
                subject: {
                    defaultValue: string;
                    overrides: any[];
                };
                introTitle: {
                    defaultValue: string;
                    overrides: any[];
                };
                introImage?: any;
                contentText: {
                    defaultValue: string;
                    overrides: any[];
                };
            };
            overrides: any[];
        };
    };
}

interface CorrectEventEmailContents {
    subject: {
        defaultValue: string;
        overrides: any[];
    };
    introTitle: {
        defaultValue: string;
        overrides: any[];
    };
    introImage?: any;
    contentText: {
        defaultValue: string;
        overrides: any[];
    };
}

export default {
    identifier: '317_fixEventEmailContentsSubmitOrderStructure',

    async up({ regular: { db } }: DatabaseContext): Promise<void> {
        console.info('Starting migration to fix event emailContents.submitOrder structure...');

        // Step 1: Find events with the incorrect nested structure
        const eventsWithIncorrectStructure = await db
            .collection<EventWithIncorrectStructure>('events')
            .find({
                'emailContents.submitOrder.subject.defaultValue.defaultValue': { $exists: true },
                'emailContents.submitOrder.introTitle.defaultValue.defaultValue': { $exists: true },
                'emailContents.submitOrder.contentText.defaultValue.defaultValue': { $exists: true },
            })
            .toArray();

        if (eventsWithIncorrectStructure.length === 0) {
            console.info('No events found with incorrect emailContents.submitOrder structure.');

            return;
        }

        console.info(`Found ${eventsWithIncorrectStructure.length} events with incorrect structure.`);

        // Step 2: Get all unique module IDs to fetch their correct email contents
        const moduleIds = [...new Set(eventsWithIncorrectStructure.map(event => event.moduleId))];

        const modules = await db
            .collection<EventApplicationModule>('modules')
            .find({
                _id: { $in: moduleIds },
                _type: ModuleType.EventApplicationModule,
                'emailContents.submitOrder.defaultValue': { $exists: true },
            })
            .toArray();

        // Create a map of moduleId to correct email contents
        const moduleEmailContentsMap = new Map<string, CorrectEventEmailContents>();

        modules.forEach(module => {
            const correctContents = module.emailContents.submitOrder.defaultValue;
            moduleEmailContentsMap.set(module._id.toString(), correctContents);
        });

        // Step 3: Prepare bulk operations to update events
        const bulkOperations: AnyBulkWriteOperation[] = [];
        let successCount = 0;
        let errorCount = 0;

        for (const event of eventsWithIncorrectStructure) {
            const moduleIdStr = event.moduleId.toString();
            const correctEmailContents = moduleEmailContentsMap.get(moduleIdStr);

            if (!correctEmailContents) {
                console.warn(`Module ${moduleIdStr} not found or missing emailContents for event ${event._id}`);
                errorCount++;
                continue;
            }

            // Validate that the replacement data has the expected structure
            if (
                !correctEmailContents.subject ||
                !correctEmailContents.introTitle ||
                !correctEmailContents.contentText
            ) {
                console.warn(`Module ${moduleIdStr} has incomplete emailContents structure for event ${event._id}`);
                errorCount++;
                continue;
            }

            bulkOperations.push({
                updateOne: {
                    filter: { _id: event._id },
                    update: {
                        $set: {
                            'emailContents.submitOrder': correctEmailContents,
                        },
                    },
                },
            });

            successCount++;
        }

        // Step 4: Execute bulk operations if any valid updates exist
        if (bulkOperations.length > 0) {
            console.info(`Executing bulk update for ${bulkOperations.length} events...`);

            try {
                const result = await db.collection('events').bulkWrite(bulkOperations, { ordered: false });
                console.info(`Successfully updated ${result.modifiedCount} events.`);
            } catch (error) {
                console.error('Error during bulk update:', error);
                throw error;
            }
        }

        // Step 5: Log summary
        console.info('Migration completed:');
        console.info(`- Events found with incorrect structure: ${eventsWithIncorrectStructure.length}`);
        console.info(`- Events successfully updated: ${successCount}`);
        console.info(`- Events with errors: ${errorCount}`);

        if (errorCount > 0) {
            console.warn(`${errorCount} events could not be updated due to missing or invalid module data.`);
        }
    },
};
