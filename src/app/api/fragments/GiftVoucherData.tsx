import type * as SchemaTypes from '../types';

import type { ModuleSpecs_AdyenPaymentModule_Fragment, ModuleSpecs_AppointmentModule_Fragment, ModuleSpecs_AutoplayModule_Fragment, ModuleSpecs_BankModule_Fragment, ModuleSpecs_BasicSigningModule_Fragment, ModuleSpecs_CapModule_Fragment, ModuleSpecs_ConfiguratorModule_Fragment, ModuleSpecs_ConsentsAndDeclarationsModule_Fragment, ModuleSpecs_CtsModule_Fragment, ModuleSpecs_DocusignModule_Fragment, ModuleSpecs_EventApplicationModule_Fragment, ModuleSpecs_FinderApplicationPrivateModule_Fragment, ModuleSpecs_FinderApplicationPublicModule_Fragment, ModuleSpecs_FinderVehicleManagementModule_Fragment, ModuleSpecs_FiservPaymentModule_Fragment, ModuleSpecs_GiftVoucherModule_Fragment, ModuleSpecs_InsuranceModule_Fragment, ModuleSpecs_LabelsModule_Fragment, ModuleSpecs_LaunchPadModule_Fragment, ModuleSpecs_LocalCustomerManagementModule_Fragment, ModuleSpecs_MaintenanceModule_Fragment, ModuleSpecs_MarketingModule_Fragment, ModuleSpecs_MobilityModule_Fragment, ModuleSpecs_MyInfoModule_Fragment, ModuleSpecs_NamirialSigningModule_Fragment, ModuleSpecs_OfrModule_Fragment, ModuleSpecs_OidcModule_Fragment, ModuleSpecs_PayGatePaymentModule_Fragment, ModuleSpecs_PorscheIdModule_Fragment, ModuleSpecs_PorscheMasterDataModule_Fragment, ModuleSpecs_PorschePaymentModule_Fragment, ModuleSpecs_PorscheRetainModule_Fragment, ModuleSpecs_PromoCodeModule_Fragment, ModuleSpecs_SalesControlBoardModule_Fragment, ModuleSpecs_SalesOfferModule_Fragment, ModuleSpecs_SimpleVehicleManagementModule_Fragment, ModuleSpecs_StandardApplicationModule_Fragment, ModuleSpecs_TradeInModule_Fragment, ModuleSpecs_TtbPaymentModule_Fragment, ModuleSpecs_UserlikeChatbotModule_Fragment, ModuleSpecs_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModuleSpecs_VisitAppointmentModule_Fragment, ModuleSpecs_WebsiteModule_Fragment, ModuleSpecs_WhatsappLiveChatModule_Fragment } from './ModuleSpecs';
import type { ConsentsAndDeclarationsModuleSpecsFragment } from './ConsentsAndDeclarationsModuleSpecs';
import type { SimpleVersioningDataFragment } from './SimpleVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import type { SimpleVehicleManagementModuleSpecsFragment } from './SimpleVehicleManagementModuleSpecs';
import type { CompanyInModuleOptionDataFragment } from './CompanyInModuleOptionData';
import type { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragment } from './VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import type { LocalCustomerManagementModuleSpecsFragment } from './LocalCustomerManagementModuleSpecs';
import type { LocalCustomerManagementModuleKycFieldSpecsFragment } from './LocalCustomerManagementModuleKycFieldSpecs';
import type { KycExtraSettingsSpecsFragment } from './KYCExtraSettingsSpecs';
import type { KycPresetsSpecFragment } from './KYCPresetsSpec';
import type { ConditionSpecs_ApplicationModuleCondition_Fragment, ConditionSpecs_BankCondition_Fragment, ConditionSpecs_ContextualCondition_Fragment, ConditionSpecs_DealerCondition_Fragment, ConditionSpecs_GiftVoucherCondition_Fragment, ConditionSpecs_InsurerCondition_Fragment, ConditionSpecs_LocationCondition_Fragment, ConditionSpecs_LogicCondition_Fragment, ConditionSpecs_SalesOfferAgreementsCondition_Fragment } from './ConditionSpecs';
import type { BaseConditionSpecs_ApplicationModuleCondition_Fragment, BaseConditionSpecs_BankCondition_Fragment, BaseConditionSpecs_ContextualCondition_Fragment, BaseConditionSpecs_DealerCondition_Fragment, BaseConditionSpecs_GiftVoucherCondition_Fragment, BaseConditionSpecs_InsurerCondition_Fragment, BaseConditionSpecs_LocationCondition_Fragment, BaseConditionSpecs_LogicCondition_Fragment, BaseConditionSpecs_SalesOfferAgreementsCondition_Fragment } from './BaseConditionSpecs';
import type { MobilityLocationDataFragment } from './MobilityLocationData';
import type { UserPreviewDataFragment } from './UserPreviewData';
import type { BankModuleSpecsFragment } from './BankModuleSpecs';
import type { BasicSigningModuleSpecsFragment } from './BasicSigningModuleSpecs';
import type { NamirialSigningModuleSpecsFragment } from './NamirialSigningModuleSpecs';
import type { NamirialSettingsSpecFragment } from './NamirialSettingsSpec';
import type { StandardApplicationModuleSpecsFragment } from './StandardApplicationModuleSpecs';
import type { DealerPriceDisclaimerDataFragment } from './DealerPriceDisclaimerData';
import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { DepositAmountDataFragment } from './DepositAmountData';
import type { ApplicationMarketTypeFragment_DefaultApplicationMarket_Fragment, ApplicationMarketTypeFragment_NewZealandApplicationMarket_Fragment, ApplicationMarketTypeFragment_SingaporeApplicationMarket_Fragment } from './ApplicationMarketTypeFragment';
import type { DealerMarketDataFragment } from './DealerMarketData';
import type { BankDealerMarketDataFragment } from './BankDealerMarketData';
import type { NzFeesDealerMarketDataFragment } from './NzFeesDealerMarketData';
import type { DealerVehiclesSpecsFragment } from './DealerVehiclesSpecs';
import type { DealerFinanceProductsSpecsFragment } from './DealerFinanceProductsSpecs';
import type { FinanceProductListData_LocalDeferredPrincipal_Fragment, FinanceProductListData_LocalHirePurchase_Fragment, FinanceProductListData_LocalHirePurchaseWithBalloon_Fragment, FinanceProductListData_LocalHirePurchaseWithBalloonGfv_Fragment, FinanceProductListData_LocalLease_Fragment, FinanceProductListData_LocalLeasePurchase_Fragment, FinanceProductListData_LocalUcclLeasing_Fragment } from './FinanceProductListData';
import type { PeriodDataFragment } from './PeriodData';
import type { ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment, ModulesCompanyTimezoneData_AppointmentModule_Fragment, ModulesCompanyTimezoneData_AutoplayModule_Fragment, ModulesCompanyTimezoneData_BankModule_Fragment, ModulesCompanyTimezoneData_BasicSigningModule_Fragment, ModulesCompanyTimezoneData_CapModule_Fragment, ModulesCompanyTimezoneData_ConfiguratorModule_Fragment, ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment, ModulesCompanyTimezoneData_CtsModule_Fragment, ModulesCompanyTimezoneData_DocusignModule_Fragment, ModulesCompanyTimezoneData_EventApplicationModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment, ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_FiservPaymentModule_Fragment, ModulesCompanyTimezoneData_GiftVoucherModule_Fragment, ModulesCompanyTimezoneData_InsuranceModule_Fragment, ModulesCompanyTimezoneData_LabelsModule_Fragment, ModulesCompanyTimezoneData_LaunchPadModule_Fragment, ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment, ModulesCompanyTimezoneData_MaintenanceModule_Fragment, ModulesCompanyTimezoneData_MarketingModule_Fragment, ModulesCompanyTimezoneData_MobilityModule_Fragment, ModulesCompanyTimezoneData_MyInfoModule_Fragment, ModulesCompanyTimezoneData_NamirialSigningModule_Fragment, ModulesCompanyTimezoneData_OfrModule_Fragment, ModulesCompanyTimezoneData_OidcModule_Fragment, ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheIdModule_Fragment, ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment, ModulesCompanyTimezoneData_PorschePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheRetainModule_Fragment, ModulesCompanyTimezoneData_PromoCodeModule_Fragment, ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment, ModulesCompanyTimezoneData_SalesOfferModule_Fragment, ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_StandardApplicationModule_Fragment, ModulesCompanyTimezoneData_TradeInModule_Fragment, ModulesCompanyTimezoneData_TtbPaymentModule_Fragment, ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment, ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment, ModulesCompanyTimezoneData_WebsiteModule_Fragment, ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment } from './ModulesCompanyTimezoneData';
import type { VehicleReferenceParametersDataFragment } from './VehicleReferenceParametersData';
import type { BalloonSettingsDetails_BalloonRangeSettings_Fragment, BalloonSettingsDetails_BalloonTableSettings_Fragment } from './BalloonSettingsDetails';
import type { BalloonGfvSettingsDetailsFragment } from './BalloonGFVSettingsDetails';
import type { DealerInsuranceProductsSpecsFragment } from './DealerInsuranceProductsSpecs';
import type { InsuranceProductListData_Eazy_Fragment, InsuranceProductListData_ErgoLookupTable_Fragment } from './InsuranceProductListData';
import type { ErgoLookupTableSettingsDetailsFragment } from './ErgoLookupTableSettingDetails';
import type { LocalModelSpecsFragment } from './LocalModelSpecs';
import type { LocalMakeSpecsFragment } from './LocalMakeSpecs';
import type { AdvancedVersioningDataFragment } from './AdvancedVersioningData';
import type { DealerDisclaimersConfiguratorDataFragment } from './DealerPriceDisclaimerConfiguratorData';
import type { KycPresetsOptionsDataFragment } from './KYCPresetsOptionsData';
import type { DealershipSettingSpecData_DealershipMyInfoSetting_Fragment, DealershipSettingSpecData_DealershipPaymentSetting_Fragment, DealershipSettingSpecData_DealershipPublicSalesPerson_Fragment } from './DealershipSettingSpecData';
import type { FlexibleDiscountDataFragment } from './FlexibleDiscountData';
import type { CounterSettingsSpecsFragment } from './CounterSettingsSpecs';
import type { StandardApplicationModuleEmailContentsSpecsFragment, StandardApplicationModuleEmailContentCustomerSpecsFragment, StandardApplicationModuleEmailContentShareSubmissionSpecsFragment, StandardApplicationModuleEmailContentSpecsFragment, StandardApplicationModuleEmailContentSalesPersonSpecsFragment } from './StandardApplicationModuleEmailContentsSpecs';
import type { DealerTranslatedStringSettingDataFragment } from './DealerTranslatedStringData';
import type { DealerUploadedFileWithPreviewDataFragment } from './DealerUploadedFileWithPreview';
import type { UploadFileWithPreviewFormDataFragment } from './UploadFileWithPreviewFormData';
import type { DealerBooleanSettingDataFragment } from './DealerBooleanSettingData';
import type { EventApplicationModuleSpecsFragment } from './EventApplicationModuleSpecs';
import type { AppointmentModuleOnEventModuleDataFragment } from './AppointmentModuleOnEventModuleData';
import type { AppointmentTimeSlotDataFragment } from './AppointmentTimeSlotData';
import type { EventApplicationModuleEmailContentSpecsFragment, EventEmailContentSpecsFragment } from './EventApplicationModuleEmailContentSpecs';
import type { TranslatedTextDataFragment } from './TranslationTextData';
import type { AdyenPaymentModuleSpecsFragment } from './AdyenPaymentModuleSpecs';
import type { AdyenPaymentSettingsSpecFragment } from './AdyenPaymentSettingsSpec';
import type { PorschePaymentModuleSpecsFragment } from './PorschePaymentModuleSpecs';
import type { PorschePaymentSettingsSpecFragment } from './PorschePaymentSettingsSpec';
import type { FiservPaymentModuleSpecsFragment } from './FiservPaymentModuleSpecs';
import type { FiservPaymentSettingsSpecFragment } from './FiservPaymentSettingsSpec';
import type { PayGatePaymentModuleSpecsFragment } from './PayGatePaymentModuleSpecs';
import type { PayGatePaymentSettingsSpecFragment } from './PayGatePaymentSettingsSpec';
import type { TtbPaymentModuleSpecsFragment } from './TtbPaymentModuleSpecs';
import type { TtbPaymentSettingsSpecFragment } from './TtbPaymentSettingsSpec';
import type { MyInfoModuleSpecsFragment } from './MyInfoModuleSpecs';
import type { MyInfoSettingSpecFragment } from './MyInfoSettingSpec';
import type { ConfiguratorModuleSpecsFragment } from './ConfiguratorModuleSpecs';
import type { ConfiguratorModuleEmailContentSpecsFragment } from './ConfiguratorModuleEmailContentSpecs';
import type { WhatsappLiveChatModuleSpecsFragment } from './WhatsappLiveChatModuleSpecs';
import type { WhatsappLiveChatSettingsSpecFragment } from './WhatsappLiveChatSettingsSpec';
import type { UserlikeChatbotModuleSpecsFragment } from './UserlikeChatbotModuleSpecs';
import type { UserlikeChatbotSettingsSpecFragment } from './UserlikeChatbotSettingsSpec';
import type { PromoCodeModuleSpecsFragment } from './PromoCodeModuleSpecs';
import type { MaintenanceModuleSpecsFragment } from './MaintenanceModuleSpecs';
import type { WebsiteModuleSpecsFragment } from './WebsiteModuleSpecs';
import type { EdmSocialMediaDataFragment } from './EdmSocialMediaData';
import type { MobilityModuleSpecsFragment } from './MobilityModuleSpecs';
import type { DealerBookingCodeSpecsFragment } from './DealerBookingCodeSpecs';
import type { MobilitySigningSettingSpecsFragment } from './MobilitySigningSettingSpecs';
import type { MobilityModuleEmailScenarioContentSpecsFragment } from './MobilityModuleEmailScenarioContentSpecs';
import type { MobilityCustomerEmailContentDataFragment } from './MobilityCustomerEmailContentData';
import type { MobilityEmailContentDataFragment } from './MobilityEmailContentData';
import type { MobilityOperatorEmailContentDataFragment } from './MobilityOperatorEmailContentData';
import type { MobilityHomeDeliveryDataFragment } from './MobilityHomeDeliveryData';
import type { LabelsModuleSpecsFragment } from './LabelsModuleSpecs';
import type { FinderVehicleManagementModuleSpecsFragment } from './FinderVehicleManagementModuleSpecs';
import type { FinderApplicationPublicModuleSpecsFragment } from './FinderApplicationPublicModuleSpecs';
import type { FinderApplicationModuleEmailContentSpecsFragment } from './FinderApplicationModuleEmailContentSpecs';
import type { ModuleDisclaimersDataFragment } from './ModuleDisclaimersData';
import type { FinderApplicationPrivateModuleSpecsFragment } from './FinderApplicationPrivateModuleSpecs';
import type { AutoplayModuleSpecsFragment } from './AutoplayModuleSpecs';
import type { AutoplaySettingSpecsFragment } from './AutoplaySettingSpecs';
import type { CtsModuleSpecsFragment } from './CtsModuleSpecs';
import type { CtsModuleSettingDataFragment } from './CtsModuleSettingData';
import type { AppointmentModuleSpecsFragment } from './AppointmentModuleSpecs';
import type { AppointmentModuleEmailContentsSpecsFragment, AppointmentModuleEmailContentCustomerSpecsFragment, AppointmentModuleEmailContentSpecsFragment, AppointmentModuleEmailContentSalesPersonSpecsFragment, AppointmentModuleEmailContentFinderReservationSpecsFragment } from './AppointmentModuleEmailContentsSpecs';
import type { InsuranceModuleSpecsFragment } from './InsuranceModuleSpecs';
import type { PorscheMasterDataModuleSpecsFragment } from './PorscheMasterDataModuleSpecs';
import type { GiftVoucherModuleSpecsFragment } from './GiftVoucherModuleSpecs';
import type { GiftVoucherModuleEmailContentsSpecsFragment, GiftVoucherModuleEmailContentCustomerSpecsFragment, GiftVoucherModuleEmailDataFragment } from './GiftVoucherModuleEmailContentsSpecs';
import type { TradeInModuleSpecsFragment } from './TradeInModuleSpecs';
import type { TradeInSettingSpecFragment } from './TradeInSetting';
import type { CapModuleSpecsFragment } from './CapModuleSpecs';
import type { CapSettingSpecFragment } from './CapSettingSpec';
import type { PorscheIdModuleSpecsFragment } from './PorscheIdModuleSpecs';
import type { PorscheIdSettingSpecFragment } from './PorscheIdSettingSpec';
import type { PorscheRetainModuleSpecsFragment } from './PorscheRetainModuleSpecs';
import type { DocusignModuleSpecsFragment } from './DocusignModuleSpecs';
import type { DocusignSettingDataFragment } from './DocusignSettingData';
import type { LaunchPadModuleSpecsFragment } from './LaunchPadModuleSpecs';
import type { VisitAppointmentModuleSpecsFragment } from './VisitAppointmentModuleSpecs';
import type { TimeSlotDataFragment } from './TimeSlotData';
import type { VisitAppointmentModuleEmailContentsSpecsFragment, VisitAppointmentModuleEmailContentCustomerSpecsFragment, VisitAppointmentModuleEmailContentSpecsFragment, VisitAppointmentModuleEmailContentSalesPersonSpecsFragment } from './VisitAppointmentModuleEmailContentsSpecs';
import type { OidcModuleSpecsFragment } from './OIDCModuleSpecs';
import type { MarketingModuleSpecsFragment } from './MarketingModuleSpecs';
import type { SalesOfferModuleSpecsFragment } from './SalesOfferModuleSpecs';
import type { BankDetailsDataFragment } from './BankDetailsData';
import type { TranslatedStringSpecsFragment } from './TranslatedStringSpecs';
import type { BankIntegrationData_DbsBankIntegration_Fragment, BankIntegrationData_EmailBankIntegration_Fragment, BankIntegrationData_EnbdBankIntegration_Fragment, BankIntegrationData_HlfBankIntegration_Fragment, BankIntegrationData_HlfBankV2Integration_Fragment, BankIntegrationData_MaybankIntegration_Fragment, BankIntegrationData_UobBankIntegration_Fragment } from './BankIntegrationData';
import type { UploadFileFormDataFragment } from './UploadFileFormData';
import type { FinanceProductDetailsData_LocalDeferredPrincipal_Fragment, FinanceProductDetailsData_LocalHirePurchase_Fragment, FinanceProductDetailsData_LocalHirePurchaseWithBalloon_Fragment, FinanceProductDetailsData_LocalHirePurchaseWithBalloonGfv_Fragment, FinanceProductDetailsData_LocalLease_Fragment, FinanceProductDetailsData_LocalLeasePurchase_Fragment, FinanceProductDetailsData_LocalUcclLeasing_Fragment } from './FinanceProductDetailsData';
import type { PaymentSettingsDetailsFragment } from './PaymentSettingsDetails';
import type { LoanSettingsDetailsFragment } from './LoanSettingsDetails';
import type { TermSettingsDetails_DeferredPrincipalTermSettings_Fragment, TermSettingsDetails_GenericPrincipalTermSettings_Fragment } from './TermSettingsDetails';
import type { InterestRateSettingsDetails_InterestRateFixedSettings_Fragment, InterestRateSettingsDetails_InterestRateRangeSettings_Fragment, InterestRateSettingsDetails_InterestRateTableSettings_Fragment } from './InterestRateSettingsDetails';
import type { DownPaymentSettingsDetails_DownPaymentRangeSettings_Fragment, DownPaymentSettingsDetails_DownPaymentTableSettings_Fragment } from './DownPaymentSettingsDetails';
import type { LeaseSettingsDetailsFragment } from './LeaseSettingsDetails';
import type { DepositSettingsDetails_DepositRangeSettings_Fragment, DepositSettingsDetails_DepositTableSettings_Fragment } from './DepositSettingsDetails';
import type { ResidualValueSettingsDetailsFragment } from './ResidualValueSettingsDetails';
import type { LocalUcclLeasingOnlyDetailsFragment } from './LocalUcclLeasingOnlyDetails';
import type { SalesOfferModuleEmailContentsSpecsFragment, SalesOfferEmailContentsSpecsFragment } from './SalesOfferModuleEmailContentsSpecs';
import type { VehicleDataWithPorscheCodeIntegrationModuleSpecsFragment } from './VehicleDataWithPorscheCodeIntegrationModuleSpecs';
import type { SalesControlBoardModuleSpecsFragment } from './SalesControlBoardModuleSpecs';
import type { DealerIntDataFragment, DealerFloatDataFragment, DealerObjectIdDataFragment } from './DealerIntData';
import type { OfrModuleSpecsFragment } from './OFRModuleSpecs';
import type { OfrModuleEmailContentsSpecsFragment, OfrSalesConsultantEmailContentSpecsFragment, OfrSalesConsultantEmailContentContextSpecsFragment, OfrCustomerEmailContentSpecsFragment, OfrCustomerEmailContentContextSpecsFragment } from './OFRModuleEmailContentsSpecs';
import type { OfrEquityDataFragment } from './OFREquityData';
import type { KycFieldSpecsFragment } from './KYCFieldSpecs';
import type { ApplicationAgreementData_CheckboxApplicationAgreement_Fragment, ApplicationAgreementData_GroupApplicationAgreement_Fragment, ApplicationAgreementData_MarketingApplicationAgreement_Fragment, ApplicationAgreementData_TextApplicationAgreement_Fragment } from './ApplicationAgreementData';
import type { MarketingPlatformSpecsFragment } from './MarketingPlatformSpecs';
import type { MarketingPlatformsAgreedSpecsFragment } from './MarketingPlatformsAgreedSpecs';
import type { MobilityModuleGiftVoucherDataFragment } from './MobilityModuleGiftVoucherData';
import type { DateUnitDataFragment } from './DateUnitData';
import type { CustomerSpecs_CorporateCustomer_Fragment, CustomerSpecs_Guarantor_Fragment, CustomerSpecs_LocalCustomer_Fragment } from './CustomerSpecs';
import type { LocalCustomerDataFragment } from './LocalCustomerData';
import type { LocalCustomerFieldData_LocalCustomerArrayStringField_Fragment, LocalCustomerFieldData_LocalCustomerDateField_Fragment, LocalCustomerFieldData_LocalCustomerDrivingLicenseField_Fragment, LocalCustomerFieldData_LocalCustomerNumberField_Fragment, LocalCustomerFieldData_LocalCustomerPhoneField_Fragment, LocalCustomerFieldData_LocalCustomerReferenceDetailSetField_Fragment, LocalCustomerFieldData_LocalCustomerSalaryTransferredBankSetField_Fragment, LocalCustomerFieldData_LocalCustomerStringDescriptionField_Fragment, LocalCustomerFieldData_LocalCustomerStringField_Fragment, LocalCustomerFieldData_LocalCustomerUaeIdentitySetField_Fragment, LocalCustomerFieldData_LocalCustomerUploadsField_Fragment, LocalCustomerFieldData_LocalCustomerVerifiedPhoneField_Fragment } from './LocalCustomerFieldData';
import type { CorporateCustomerDataFragment } from './CorporateCustomerData';
import type { GuarantorDataFragment } from './GuarantorData';
import type { GiftVoucherDraftFlowDataFragment } from './GiftVoucherDraftFlowData';
import type { ApplicationAdyenDepositDataFragment } from './ApplicationAdyenDepositData';
import type { ApplicationPorscheDepositDataFragment } from './ApplicationPorscheDepositData';
import type { ApplicationFiservDepositDataFragment } from './ApplicationFiservDepositData';
import type { ApplicationPayGateDepositDataFragment } from './ApplicationPayGateDepositData';
import type { ApplicationTtbDepositDataFragment } from './ApplicationTtbDepositData';
import type { LocalVariantPublicSpecsFragment } from './LocalVariantPublicSpecs';
import type { LocalModelPublicSpecsFragment } from './LocalModelPublicSpecs';
import type { LocalMakePublicSpecsFragment } from './LocalMakePublicSpecs';
import type { DealerJourneyDataFragment } from './DealerJourneyData';
import type { MobilityStockGiftVoucherDataFragment } from './MobilityStockGiftVoucherData';
import type { CompanyPublicSpecsFragment } from './CompanyPublicSpecs';
import type { ApplicationDocumentDataFragment } from './ApplicationDocumentData';
import type { ReferenceApplicationData_ConfiguratorApplication_Fragment, ReferenceApplicationData_EventApplication_Fragment, ReferenceApplicationData_FinderApplication_Fragment, ReferenceApplicationData_LaunchpadApplication_Fragment, ReferenceApplicationData_MobilityApplication_Fragment, ReferenceApplicationData_SalesOfferApplication_Fragment, ReferenceApplicationData_StandardApplication_Fragment, ReferenceDepositData_ApplicationAdyenDeposit_Fragment, ReferenceDepositData_ApplicationFiservDeposit_Fragment, ReferenceDepositData_ApplicationPayGateDeposit_Fragment, ReferenceDepositData_ApplicationPorscheDeposit_Fragment, ReferenceDepositData_ApplicationTtbDeposit_Fragment, ReferenceFinancingData_DefaultApplicationFinancing_Fragment, ReferenceFinancingData_NewZealandApplicationFinancing_Fragment, ReferenceFinancingData_SingaporeApplicationFinancing_Fragment, ReferenceInsuranceData_DefaultApplicationInsurancing_Fragment, ReferenceInsuranceData_NewZealandApplicationInsurancing_Fragment, ReferenceInsuranceData_SingaporeApplicationInsurancing_Fragment } from './ReferenceApplicationData';
import type { ApplicationStageData_ConfiguratorApplication_Fragment, ApplicationStageData_EventApplication_Fragment, ApplicationStageData_FinderApplication_Fragment, ApplicationStageData_LaunchpadApplication_Fragment, ApplicationStageData_MobilityApplication_Fragment, ApplicationStageData_SalesOfferApplication_Fragment, ApplicationStageData_StandardApplication_Fragment } from './ApplicationStageData';
import type { VehicleSpecs_FinderVehicle_Fragment, VehicleSpecs_LocalMake_Fragment, VehicleSpecs_LocalModel_Fragment, VehicleSpecs_LocalVariant_Fragment } from './VehicleSpecs';
import type { LocalVariantBaseSpecsFragment } from './LocalVariantBaseSpecs';
import type { FinderVehicleSpecsFragment } from './FinderVehicleSpecs';
import type { FullListingValueFragment, FormattedDateDataFragment, LocalizedStringDataFragment, LocalizedValueDataFragment, NumberUnitDataFragment } from './finderListing.fragment';
import type { TradeInVehicleDataFragment } from './TradeInVehicleData';
import { gql } from '@apollo/client';
import { ModuleSpecsFragmentDoc } from './ModuleSpecs';
import { ConsentsAndDeclarationsModuleSpecsFragmentDoc } from './ConsentsAndDeclarationsModuleSpecs';
import { SimpleVersioningDataFragmentDoc } from './SimpleVersioningData';
import { AuthorDataFragmentDoc } from './AuthorData';
import { SimpleVehicleManagementModuleSpecsFragmentDoc } from './SimpleVehicleManagementModuleSpecs';
import { CompanyInModuleOptionDataFragmentDoc } from './CompanyInModuleOptionData';
import { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc } from './VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import { LocalCustomerManagementModuleSpecsFragmentDoc } from './LocalCustomerManagementModuleSpecs';
import { LocalCustomerManagementModuleKycFieldSpecsFragmentDoc } from './LocalCustomerManagementModuleKycFieldSpecs';
import { KycExtraSettingsSpecsFragmentDoc } from './KYCExtraSettingsSpecs';
import { KycPresetsSpecFragmentDoc } from './KYCPresetsSpec';
import { ConditionSpecsFragmentDoc } from './ConditionSpecs';
import { BaseConditionSpecsFragmentDoc } from './BaseConditionSpecs';
import { MobilityLocationDataFragmentDoc } from './MobilityLocationData';
import { UserPreviewDataFragmentDoc } from './UserPreviewData';
import { BankModuleSpecsFragmentDoc } from './BankModuleSpecs';
import { BasicSigningModuleSpecsFragmentDoc } from './BasicSigningModuleSpecs';
import { NamirialSigningModuleSpecsFragmentDoc } from './NamirialSigningModuleSpecs';
import { NamirialSettingsSpecFragmentDoc } from './NamirialSettingsSpec';
import { StandardApplicationModuleSpecsFragmentDoc } from './StandardApplicationModuleSpecs';
import { DealerPriceDisclaimerDataFragmentDoc } from './DealerPriceDisclaimerData';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { DepositAmountDataFragmentDoc } from './DepositAmountData';
import { ApplicationMarketTypeFragmentFragmentDoc } from './ApplicationMarketTypeFragment';
import { DealerMarketDataFragmentDoc } from './DealerMarketData';
import { BankDealerMarketDataFragmentDoc } from './BankDealerMarketData';
import { NzFeesDealerMarketDataFragmentDoc } from './NzFeesDealerMarketData';
import { DealerVehiclesSpecsFragmentDoc } from './DealerVehiclesSpecs';
import { DealerFinanceProductsSpecsFragmentDoc } from './DealerFinanceProductsSpecs';
import { FinanceProductListDataFragmentDoc } from './FinanceProductListData';
import { PeriodDataFragmentDoc } from './PeriodData';
import { ModulesCompanyTimezoneDataFragmentDoc } from './ModulesCompanyTimezoneData';
import { VehicleReferenceParametersDataFragmentDoc } from './VehicleReferenceParametersData';
import { BalloonSettingsDetailsFragmentDoc } from './BalloonSettingsDetails';
import { BalloonGfvSettingsDetailsFragmentDoc } from './BalloonGFVSettingsDetails';
import { DealerInsuranceProductsSpecsFragmentDoc } from './DealerInsuranceProductsSpecs';
import { InsuranceProductListDataFragmentDoc } from './InsuranceProductListData';
import { ErgoLookupTableSettingsDetailsFragmentDoc } from './ErgoLookupTableSettingDetails';
import { LocalModelSpecsFragmentDoc } from './LocalModelSpecs';
import { LocalMakeSpecsFragmentDoc } from './LocalMakeSpecs';
import { AdvancedVersioningDataFragmentDoc } from './AdvancedVersioningData';
import { DealerDisclaimersConfiguratorDataFragmentDoc } from './DealerPriceDisclaimerConfiguratorData';
import { KycPresetsOptionsDataFragmentDoc } from './KYCPresetsOptionsData';
import { DealershipSettingSpecDataFragmentDoc } from './DealershipSettingSpecData';
import { FlexibleDiscountDataFragmentDoc } from './FlexibleDiscountData';
import { CounterSettingsSpecsFragmentDoc } from './CounterSettingsSpecs';
import { StandardApplicationModuleEmailContentsSpecsFragmentDoc, StandardApplicationModuleEmailContentCustomerSpecsFragmentDoc, StandardApplicationModuleEmailContentShareSubmissionSpecsFragmentDoc, StandardApplicationModuleEmailContentSpecsFragmentDoc, StandardApplicationModuleEmailContentSalesPersonSpecsFragmentDoc } from './StandardApplicationModuleEmailContentsSpecs';
import { DealerTranslatedStringSettingDataFragmentDoc } from './DealerTranslatedStringData';
import { DealerUploadedFileWithPreviewDataFragmentDoc } from './DealerUploadedFileWithPreview';
import { UploadFileWithPreviewFormDataFragmentDoc } from './UploadFileWithPreviewFormData';
import { DealerBooleanSettingDataFragmentDoc } from './DealerBooleanSettingData';
import { EventApplicationModuleSpecsFragmentDoc } from './EventApplicationModuleSpecs';
import { AppointmentModuleOnEventModuleDataFragmentDoc } from './AppointmentModuleOnEventModuleData';
import { AppointmentTimeSlotDataFragmentDoc } from './AppointmentTimeSlotData';
import { EventApplicationModuleEmailContentSpecsFragmentDoc, EventEmailContentSpecsFragmentDoc } from './EventApplicationModuleEmailContentSpecs';
import { TranslatedTextDataFragmentDoc } from './TranslationTextData';
import { AdyenPaymentModuleSpecsFragmentDoc } from './AdyenPaymentModuleSpecs';
import { AdyenPaymentSettingsSpecFragmentDoc } from './AdyenPaymentSettingsSpec';
import { PorschePaymentModuleSpecsFragmentDoc } from './PorschePaymentModuleSpecs';
import { PorschePaymentSettingsSpecFragmentDoc } from './PorschePaymentSettingsSpec';
import { FiservPaymentModuleSpecsFragmentDoc } from './FiservPaymentModuleSpecs';
import { FiservPaymentSettingsSpecFragmentDoc } from './FiservPaymentSettingsSpec';
import { PayGatePaymentModuleSpecsFragmentDoc } from './PayGatePaymentModuleSpecs';
import { PayGatePaymentSettingsSpecFragmentDoc } from './PayGatePaymentSettingsSpec';
import { TtbPaymentModuleSpecsFragmentDoc } from './TtbPaymentModuleSpecs';
import { TtbPaymentSettingsSpecFragmentDoc } from './TtbPaymentSettingsSpec';
import { MyInfoModuleSpecsFragmentDoc } from './MyInfoModuleSpecs';
import { MyInfoSettingSpecFragmentDoc } from './MyInfoSettingSpec';
import { ConfiguratorModuleSpecsFragmentDoc } from './ConfiguratorModuleSpecs';
import { ConfiguratorModuleEmailContentSpecsFragmentDoc } from './ConfiguratorModuleEmailContentSpecs';
import { WhatsappLiveChatModuleSpecsFragmentDoc } from './WhatsappLiveChatModuleSpecs';
import { WhatsappLiveChatSettingsSpecFragmentDoc } from './WhatsappLiveChatSettingsSpec';
import { UserlikeChatbotModuleSpecsFragmentDoc } from './UserlikeChatbotModuleSpecs';
import { UserlikeChatbotSettingsSpecFragmentDoc } from './UserlikeChatbotSettingsSpec';
import { PromoCodeModuleSpecsFragmentDoc } from './PromoCodeModuleSpecs';
import { MaintenanceModuleSpecsFragmentDoc } from './MaintenanceModuleSpecs';
import { WebsiteModuleSpecsFragmentDoc } from './WebsiteModuleSpecs';
import { EdmSocialMediaDataFragmentDoc } from './EdmSocialMediaData';
import { MobilityModuleSpecsFragmentDoc } from './MobilityModuleSpecs';
import { DealerBookingCodeSpecsFragmentDoc } from './DealerBookingCodeSpecs';
import { MobilitySigningSettingSpecsFragmentDoc } from './MobilitySigningSettingSpecs';
import { MobilityModuleEmailScenarioContentSpecsFragmentDoc } from './MobilityModuleEmailScenarioContentSpecs';
import { MobilityCustomerEmailContentDataFragmentDoc } from './MobilityCustomerEmailContentData';
import { MobilityEmailContentDataFragmentDoc } from './MobilityEmailContentData';
import { MobilityOperatorEmailContentDataFragmentDoc } from './MobilityOperatorEmailContentData';
import { MobilityHomeDeliveryDataFragmentDoc } from './MobilityHomeDeliveryData';
import { LabelsModuleSpecsFragmentDoc } from './LabelsModuleSpecs';
import { FinderVehicleManagementModuleSpecsFragmentDoc } from './FinderVehicleManagementModuleSpecs';
import { FinderApplicationPublicModuleSpecsFragmentDoc } from './FinderApplicationPublicModuleSpecs';
import { FinderApplicationModuleEmailContentSpecsFragmentDoc } from './FinderApplicationModuleEmailContentSpecs';
import { ModuleDisclaimersDataFragmentDoc } from './ModuleDisclaimersData';
import { FinderApplicationPrivateModuleSpecsFragmentDoc } from './FinderApplicationPrivateModuleSpecs';
import { AutoplayModuleSpecsFragmentDoc } from './AutoplayModuleSpecs';
import { AutoplaySettingSpecsFragmentDoc } from './AutoplaySettingSpecs';
import { CtsModuleSpecsFragmentDoc } from './CtsModuleSpecs';
import { CtsModuleSettingDataFragmentDoc } from './CtsModuleSettingData';
import { AppointmentModuleSpecsFragmentDoc } from './AppointmentModuleSpecs';
import { AppointmentModuleEmailContentsSpecsFragmentDoc, AppointmentModuleEmailContentCustomerSpecsFragmentDoc, AppointmentModuleEmailContentSpecsFragmentDoc, AppointmentModuleEmailContentSalesPersonSpecsFragmentDoc, AppointmentModuleEmailContentFinderReservationSpecsFragmentDoc } from './AppointmentModuleEmailContentsSpecs';
import { InsuranceModuleSpecsFragmentDoc } from './InsuranceModuleSpecs';
import { PorscheMasterDataModuleSpecsFragmentDoc } from './PorscheMasterDataModuleSpecs';
import { GiftVoucherModuleSpecsFragmentDoc } from './GiftVoucherModuleSpecs';
import { GiftVoucherModuleEmailContentsSpecsFragmentDoc, GiftVoucherModuleEmailContentCustomerSpecsFragmentDoc, GiftVoucherModuleEmailDataFragmentDoc } from './GiftVoucherModuleEmailContentsSpecs';
import { TradeInModuleSpecsFragmentDoc } from './TradeInModuleSpecs';
import { TradeInSettingSpecFragmentDoc } from './TradeInSetting';
import { CapModuleSpecsFragmentDoc } from './CapModuleSpecs';
import { CapSettingSpecFragmentDoc } from './CapSettingSpec';
import { PorscheIdModuleSpecsFragmentDoc } from './PorscheIdModuleSpecs';
import { PorscheIdSettingSpecFragmentDoc } from './PorscheIdSettingSpec';
import { PorscheRetainModuleSpecsFragmentDoc } from './PorscheRetainModuleSpecs';
import { DocusignModuleSpecsFragmentDoc } from './DocusignModuleSpecs';
import { DocusignSettingDataFragmentDoc } from './DocusignSettingData';
import { LaunchPadModuleSpecsFragmentDoc } from './LaunchPadModuleSpecs';
import { VisitAppointmentModuleSpecsFragmentDoc } from './VisitAppointmentModuleSpecs';
import { TimeSlotDataFragmentDoc } from './TimeSlotData';
import { VisitAppointmentModuleEmailContentsSpecsFragmentDoc, VisitAppointmentModuleEmailContentCustomerSpecsFragmentDoc, VisitAppointmentModuleEmailContentSpecsFragmentDoc, VisitAppointmentModuleEmailContentSalesPersonSpecsFragmentDoc } from './VisitAppointmentModuleEmailContentsSpecs';
import { OidcModuleSpecsFragmentDoc } from './OIDCModuleSpecs';
import { MarketingModuleSpecsFragmentDoc } from './MarketingModuleSpecs';
import { SalesOfferModuleSpecsFragmentDoc } from './SalesOfferModuleSpecs';
import { BankDetailsDataFragmentDoc } from './BankDetailsData';
import { TranslatedStringSpecsFragmentDoc } from './TranslatedStringSpecs';
import { BankIntegrationDataFragmentDoc } from './BankIntegrationData';
import { UploadFileFormDataFragmentDoc } from './UploadFileFormData';
import { FinanceProductDetailsDataFragmentDoc } from './FinanceProductDetailsData';
import { PaymentSettingsDetailsFragmentDoc } from './PaymentSettingsDetails';
import { LoanSettingsDetailsFragmentDoc } from './LoanSettingsDetails';
import { TermSettingsDetailsFragmentDoc } from './TermSettingsDetails';
import { InterestRateSettingsDetailsFragmentDoc } from './InterestRateSettingsDetails';
import { DownPaymentSettingsDetailsFragmentDoc } from './DownPaymentSettingsDetails';
import { LeaseSettingsDetailsFragmentDoc } from './LeaseSettingsDetails';
import { DepositSettingsDetailsFragmentDoc } from './DepositSettingsDetails';
import { ResidualValueSettingsDetailsFragmentDoc } from './ResidualValueSettingsDetails';
import { LocalUcclLeasingOnlyDetailsFragmentDoc } from './LocalUcclLeasingOnlyDetails';
import { SalesOfferModuleEmailContentsSpecsFragmentDoc, SalesOfferEmailContentsSpecsFragmentDoc } from './SalesOfferModuleEmailContentsSpecs';
import { VehicleDataWithPorscheCodeIntegrationModuleSpecsFragmentDoc } from './VehicleDataWithPorscheCodeIntegrationModuleSpecs';
import { SalesControlBoardModuleSpecsFragmentDoc } from './SalesControlBoardModuleSpecs';
import { DealerIntDataFragmentDoc, DealerFloatDataFragmentDoc, DealerObjectIdDataFragmentDoc } from './DealerIntData';
import { OfrModuleSpecsFragmentDoc } from './OFRModuleSpecs';
import { OfrModuleEmailContentsSpecsFragmentDoc, OfrSalesConsultantEmailContentSpecsFragmentDoc, OfrSalesConsultantEmailContentContextSpecsFragmentDoc, OfrCustomerEmailContentSpecsFragmentDoc, OfrCustomerEmailContentContextSpecsFragmentDoc } from './OFRModuleEmailContentsSpecs';
import { OfrEquityDataFragmentDoc } from './OFREquityData';
import { KycFieldSpecsFragmentDoc } from './KYCFieldSpecs';
import { ApplicationAgreementDataFragmentDoc } from './ApplicationAgreementData';
import { MarketingPlatformSpecsFragmentDoc } from './MarketingPlatformSpecs';
import { MarketingPlatformsAgreedSpecsFragmentDoc } from './MarketingPlatformsAgreedSpecs';
import { MobilityModuleGiftVoucherDataFragmentDoc } from './MobilityModuleGiftVoucherData';
import { DateUnitDataFragmentDoc } from './DateUnitData';
import { CustomerSpecsFragmentDoc } from './CustomerSpecs';
import { LocalCustomerDataFragmentDoc } from './LocalCustomerData';
import { LocalCustomerFieldDataFragmentDoc } from './LocalCustomerFieldData';
import { CorporateCustomerDataFragmentDoc } from './CorporateCustomerData';
import { GuarantorDataFragmentDoc } from './GuarantorData';
import { GiftVoucherDraftFlowDataFragmentDoc } from './GiftVoucherDraftFlowData';
import { ApplicationAdyenDepositDataFragmentDoc } from './ApplicationAdyenDepositData';
import { ApplicationPorscheDepositDataFragmentDoc } from './ApplicationPorscheDepositData';
import { ApplicationFiservDepositDataFragmentDoc } from './ApplicationFiservDepositData';
import { ApplicationPayGateDepositDataFragmentDoc } from './ApplicationPayGateDepositData';
import { ApplicationTtbDepositDataFragmentDoc } from './ApplicationTtbDepositData';
import { LocalVariantPublicSpecsFragmentDoc } from './LocalVariantPublicSpecs';
import { LocalModelPublicSpecsFragmentDoc } from './LocalModelPublicSpecs';
import { LocalMakePublicSpecsFragmentDoc } from './LocalMakePublicSpecs';
import { DealerJourneyDataFragmentDoc } from './DealerJourneyData';
import { MobilityStockGiftVoucherDataFragmentDoc } from './MobilityStockGiftVoucherData';
import { CompanyPublicSpecsFragmentDoc } from './CompanyPublicSpecs';
import { ApplicationDocumentDataFragmentDoc } from './ApplicationDocumentData';
import { ReferenceApplicationDataFragmentDoc, ReferenceDepositDataFragmentDoc, ReferenceFinancingDataFragmentDoc, ReferenceInsuranceDataFragmentDoc } from './ReferenceApplicationData';
import { ApplicationStageDataFragmentDoc } from './ApplicationStageData';
import { VehicleSpecsFragmentDoc } from './VehicleSpecs';
import { LocalVariantBaseSpecsFragmentDoc } from './LocalVariantBaseSpecs';
import { FinderVehicleSpecsFragmentDoc } from './FinderVehicleSpecs';
import { FullListingValueFragmentDoc, FormattedDateDataFragmentDoc, LocalizedStringDataFragmentDoc, LocalizedValueDataFragmentDoc, NumberUnitDataFragmentDoc } from './finderListing.fragment';
import { TradeInVehicleDataFragmentDoc } from './TradeInVehicleData';
export type GiftVoucherDataFragment = (
  { __typename: 'GiftVoucher' }
  & Pick<SchemaTypes.GiftVoucher, 'id' | 'giftCode' | 'purchasedDate' | 'status' | 'value' | 'moduleId' | 'numberOfBookingReferenceDays' | 'purchaserFullName' | 'giftee' | 'expireAt'>
  & { module: (
    { __typename: 'AdyenPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_AdyenPaymentModule_Fragment
  ) | (
    { __typename: 'AppointmentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_AppointmentModule_Fragment
  ) | (
    { __typename: 'AutoplayModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_AutoplayModule_Fragment
  ) | (
    { __typename: 'BankModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_BankModule_Fragment
  ) | (
    { __typename: 'BasicSigningModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_BasicSigningModule_Fragment
  ) | (
    { __typename: 'CapModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_CapModule_Fragment
  ) | (
    { __typename: 'ConfiguratorModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_ConfiguratorModule_Fragment
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_ConsentsAndDeclarationsModule_Fragment
  ) | (
    { __typename: 'CtsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_CtsModule_Fragment
  ) | (
    { __typename: 'DocusignModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_DocusignModule_Fragment
  ) | (
    { __typename: 'EventApplicationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_EventApplicationModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_FinderApplicationPrivateModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_FinderApplicationPublicModule_Fragment
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_FinderVehicleManagementModule_Fragment
  ) | (
    { __typename: 'FiservPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_FiservPaymentModule_Fragment
  ) | (
    { __typename: 'GiftVoucherModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_GiftVoucherModule_Fragment
  ) | (
    { __typename: 'InsuranceModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_InsuranceModule_Fragment
  ) | (
    { __typename: 'LabelsModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_LabelsModule_Fragment
  ) | (
    { __typename: 'LaunchPadModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_LaunchPadModule_Fragment
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_LocalCustomerManagementModule_Fragment
  ) | (
    { __typename: 'MaintenanceModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_MaintenanceModule_Fragment
  ) | (
    { __typename: 'MarketingModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_MarketingModule_Fragment
  ) | (
    { __typename: 'MobilityModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_MobilityModule_Fragment
  ) | (
    { __typename: 'MyInfoModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_MyInfoModule_Fragment
  ) | (
    { __typename: 'NamirialSigningModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_NamirialSigningModule_Fragment
  ) | (
    { __typename: 'OFRModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_OfrModule_Fragment
  ) | (
    { __typename: 'OIDCModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_OidcModule_Fragment
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_PayGatePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheIdModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_PorscheIdModule_Fragment
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_PorscheMasterDataModule_Fragment
  ) | (
    { __typename: 'PorschePaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_PorschePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheRetainModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_PorscheRetainModule_Fragment
  ) | (
    { __typename: 'PromoCodeModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_PromoCodeModule_Fragment
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_SalesControlBoardModule_Fragment
  ) | (
    { __typename: 'SalesOfferModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_SalesOfferModule_Fragment
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_SimpleVehicleManagementModule_Fragment
  ) | (
    { __typename: 'StandardApplicationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_StandardApplicationModule_Fragment
  ) | (
    { __typename: 'TradeInModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_TradeInModule_Fragment
  ) | (
    { __typename: 'TtbPaymentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_TtbPaymentModule_Fragment
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_UserlikeChatbotModule_Fragment
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_VehicleDataWithPorscheCodeIntegrationModule_Fragment
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_VisitAppointmentModule_Fragment
  ) | (
    { __typename: 'WebsiteModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_WebsiteModule_Fragment
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & { company: (
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id'>
      & { roundings: (
        { __typename: 'Roundings' }
        & { amount: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ), percentage: (
          { __typename: 'Rounding' }
          & Pick<SchemaTypes.Rounding, 'decimals'>
        ) }
      ) }
    ) }
    & ModuleSpecs_WhatsappLiveChatModule_Fragment
  ), purchaserKYC: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )>, purchaserAgreements: Array<(
    { __typename: 'CheckboxApplicationAgreement' }
    & ApplicationAgreementData_CheckboxApplicationAgreement_Fragment
  ) | (
    { __typename: 'GroupApplicationAgreement' }
    & ApplicationAgreementData_GroupApplicationAgreement_Fragment
  ) | (
    { __typename: 'MarketingApplicationAgreement' }
    & ApplicationAgreementData_MarketingApplicationAgreement_Fragment
  ) | (
    { __typename: 'TextApplicationAgreement' }
    & ApplicationAgreementData_TextApplicationAgreement_Fragment
  )>, mobilityModule: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | { __typename: 'EventApplicationModule' } | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | { __typename: 'LocalCustomerManagementModule' } | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | (
    { __typename: 'MobilityModule' }
    & MobilityModuleGiftVoucherDataFragment
  ) | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OFRModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, purchaser?: SchemaTypes.Maybe<(
    { __typename: 'CorporateCustomer' }
    & CustomerSpecs_CorporateCustomer_Fragment
  ) | (
    { __typename: 'Guarantor' }
    & CustomerSpecs_Guarantor_Fragment
  ) | (
    { __typename: 'LocalCustomer' }
    & CustomerSpecs_LocalCustomer_Fragment
  )>, draftFlow: (
    { __typename: 'GiftVoucherDraftFlow' }
    & GiftVoucherDraftFlowDataFragment
  ), deposit?: SchemaTypes.Maybe<(
    { __typename: 'ApplicationAdyenDeposit' }
    & ApplicationAdyenDepositDataFragment
  ) | (
    { __typename: 'ApplicationFiservDeposit' }
    & ApplicationFiservDepositDataFragment
  ) | (
    { __typename: 'ApplicationPayGateDeposit' }
    & ApplicationPayGateDepositDataFragment
  ) | (
    { __typename: 'ApplicationPorscheDeposit' }
    & ApplicationPorscheDepositDataFragment
  ) | (
    { __typename: 'ApplicationTtbDeposit' }
    & ApplicationTtbDepositDataFragment
  )>, versioning: (
    { __typename: 'AdvancedVersioning' }
    & AdvancedVersioningDataFragment
  ), vehicle: { __typename: 'FinderVehicle' } | { __typename: 'LocalMake' } | { __typename: 'LocalModel' } | (
    { __typename: 'LocalVariant' }
    & LocalVariantPublicSpecsFragment
  ), dealer?: SchemaTypes.Maybe<(
    { __typename: 'Dealer' }
    & DealerJourneyDataFragment
  )>, stock?: SchemaTypes.Maybe<{ __typename: 'ConfiguratorStockInventory' } | (
    { __typename: 'MobilityStockInventory' }
    & MobilityStockGiftVoucherDataFragment
  )>, documents: Array<(
    { __typename: 'ApplicationDocument' }
    & ApplicationDocumentDataFragment
  )> }
);

export type GiftVoucherSpecsFragment = (
  { __typename: 'GiftVoucher' }
  & { application?: SchemaTypes.Maybe<(
    { __typename: 'MobilityApplication' }
    & ReferenceApplicationData_MobilityApplication_Fragment
  )> }
  & GiftVoucherDataFragment
);

export const GiftVoucherDataFragmentDoc = /*#__PURE__*/ gql`
    fragment GiftVoucherData on GiftVoucher {
  id
  giftCode
  purchasedDate
  status
  value
  moduleId
  module {
    ...ModuleSpecs
    company {
      id
      roundings {
        amount {
          decimals
        }
        percentage {
          decimals
        }
      }
    }
  }
  numberOfBookingReferenceDays
  purchaserKYC {
    ...KYCFieldSpecs
  }
  purchaserFullName
  purchaserAgreements {
    ...ApplicationAgreementData
  }
  mobilityModule {
    ...MobilityModuleGiftVoucherData
  }
  purchaser {
    ...CustomerSpecs
  }
  draftFlow {
    ...GiftVoucherDraftFlowData
  }
  deposit {
    ... on ApplicationAdyenDeposit {
      ...ApplicationAdyenDepositData
    }
    ... on ApplicationPorscheDeposit {
      ...ApplicationPorscheDepositData
    }
    ... on ApplicationFiservDeposit {
      ...ApplicationFiservDepositData
    }
    ... on ApplicationPayGateDeposit {
      ...ApplicationPayGateDepositData
    }
    ... on ApplicationTtbDeposit {
      ...ApplicationTtbDepositData
    }
  }
  versioning {
    ...AdvancedVersioningData
  }
  vehicle {
    ...LocalVariantPublicSpecs
  }
  dealer {
    ...DealerJourneyData
  }
  stock {
    ...MobilityStockGiftVoucherData
  }
  documents {
    ...ApplicationDocumentData
  }
  giftee
  expireAt
}
    `;
export const GiftVoucherSpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment GiftVoucherSpecs on GiftVoucher {
  ...GiftVoucherData
  application {
    ...ReferenceApplicationData
  }
}
    `;