import type * as SchemaTypes from '../types';

import type { ConsentsAndDeclarationsModuleSpecsFragment } from './ConsentsAndDeclarationsModuleSpecs';
import type { SimpleVersioningDataFragment } from './SimpleVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import type { SimpleVehicleManagementModuleSpecsFragment } from './SimpleVehicleManagementModuleSpecs';
import type { CompanyInModuleOptionDataFragment } from './CompanyInModuleOptionData';
import type { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragment } from './VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import type { LocalCustomerManagementModuleSpecsFragment } from './LocalCustomerManagementModuleSpecs';
import type { LocalCustomerManagementModuleKycFieldSpecsFragment } from './LocalCustomerManagementModuleKycFieldSpecs';
import type { KycExtraSettingsSpecsFragment } from './KYCExtraSettingsSpecs';
import type { KycPresetsSpecFragment } from './KYCPresetsSpec';
import type { ConditionSpecs_ApplicationModuleCondition_Fragment, ConditionSpecs_BankCondition_Fragment, ConditionSpecs_ContextualCondition_Fragment, ConditionSpecs_DealerCondition_Fragment, ConditionSpecs_GiftVoucherCondition_Fragment, ConditionSpecs_InsurerCondition_Fragment, ConditionSpecs_LocationCondition_Fragment, ConditionSpecs_LogicCondition_Fragment, ConditionSpecs_SalesOfferAgreementsCondition_Fragment } from './ConditionSpecs';
import type { BaseConditionSpecs_ApplicationModuleCondition_Fragment, BaseConditionSpecs_BankCondition_Fragment, BaseConditionSpecs_ContextualCondition_Fragment, BaseConditionSpecs_DealerCondition_Fragment, BaseConditionSpecs_GiftVoucherCondition_Fragment, BaseConditionSpecs_InsurerCondition_Fragment, BaseConditionSpecs_LocationCondition_Fragment, BaseConditionSpecs_LogicCondition_Fragment, BaseConditionSpecs_SalesOfferAgreementsCondition_Fragment } from './BaseConditionSpecs';
import type { MobilityLocationDataFragment } from './MobilityLocationData';
import type { UserPreviewDataFragment } from './UserPreviewData';
import type { BankModuleSpecsFragment } from './BankModuleSpecs';
import type { BasicSigningModuleSpecsFragment } from './BasicSigningModuleSpecs';
import type { NamirialSigningModuleSpecsFragment } from './NamirialSigningModuleSpecs';
import type { NamirialSettingsSpecFragment } from './NamirialSettingsSpec';
import type { StandardApplicationModuleSpecsFragment } from './StandardApplicationModuleSpecs';
import type { DealerPriceDisclaimerDataFragment } from './DealerPriceDisclaimerData';
import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { DepositAmountDataFragment } from './DepositAmountData';
import type { ApplicationMarketTypeFragment_DefaultApplicationMarket_Fragment, ApplicationMarketTypeFragment_NewZealandApplicationMarket_Fragment, ApplicationMarketTypeFragment_SingaporeApplicationMarket_Fragment } from './ApplicationMarketTypeFragment';
import type { DealerMarketDataFragment } from './DealerMarketData';
import type { BankDealerMarketDataFragment } from './BankDealerMarketData';
import type { NzFeesDealerMarketDataFragment } from './NzFeesDealerMarketData';
import type { DealerVehiclesSpecsFragment } from './DealerVehiclesSpecs';
import type { DealerFinanceProductsSpecsFragment } from './DealerFinanceProductsSpecs';
import type { FinanceProductListData_LocalDeferredPrincipal_Fragment, FinanceProductListData_LocalHirePurchase_Fragment, FinanceProductListData_LocalHirePurchaseWithBalloon_Fragment, FinanceProductListData_LocalHirePurchaseWithBalloonGfv_Fragment, FinanceProductListData_LocalLease_Fragment, FinanceProductListData_LocalLeasePurchase_Fragment, FinanceProductListData_LocalUcclLeasing_Fragment } from './FinanceProductListData';
import type { PeriodDataFragment } from './PeriodData';
import type { ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment, ModulesCompanyTimezoneData_AppointmentModule_Fragment, ModulesCompanyTimezoneData_AutoplayModule_Fragment, ModulesCompanyTimezoneData_BankModule_Fragment, ModulesCompanyTimezoneData_BasicSigningModule_Fragment, ModulesCompanyTimezoneData_CapModule_Fragment, ModulesCompanyTimezoneData_ConfiguratorModule_Fragment, ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment, ModulesCompanyTimezoneData_CtsModule_Fragment, ModulesCompanyTimezoneData_DocusignModule_Fragment, ModulesCompanyTimezoneData_EventApplicationModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment, ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_FiservPaymentModule_Fragment, ModulesCompanyTimezoneData_GiftVoucherModule_Fragment, ModulesCompanyTimezoneData_InsuranceModule_Fragment, ModulesCompanyTimezoneData_LabelsModule_Fragment, ModulesCompanyTimezoneData_LaunchPadModule_Fragment, ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment, ModulesCompanyTimezoneData_MaintenanceModule_Fragment, ModulesCompanyTimezoneData_MarketingModule_Fragment, ModulesCompanyTimezoneData_MobilityModule_Fragment, ModulesCompanyTimezoneData_MyInfoModule_Fragment, ModulesCompanyTimezoneData_NamirialSigningModule_Fragment, ModulesCompanyTimezoneData_OfrModule_Fragment, ModulesCompanyTimezoneData_OidcModule_Fragment, ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheIdModule_Fragment, ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment, ModulesCompanyTimezoneData_PorschePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheRetainModule_Fragment, ModulesCompanyTimezoneData_PromoCodeModule_Fragment, ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment, ModulesCompanyTimezoneData_SalesOfferModule_Fragment, ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_StandardApplicationModule_Fragment, ModulesCompanyTimezoneData_TradeInModule_Fragment, ModulesCompanyTimezoneData_TtbPaymentModule_Fragment, ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment, ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment, ModulesCompanyTimezoneData_WebsiteModule_Fragment, ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment } from './ModulesCompanyTimezoneData';
import type { VehicleReferenceParametersDataFragment } from './VehicleReferenceParametersData';
import type { BalloonSettingsDetails_BalloonRangeSettings_Fragment, BalloonSettingsDetails_BalloonTableSettings_Fragment } from './BalloonSettingsDetails';
import type { BalloonGfvSettingsDetailsFragment } from './BalloonGFVSettingsDetails';
import type { DealerInsuranceProductsSpecsFragment } from './DealerInsuranceProductsSpecs';
import type { InsuranceProductListData_Eazy_Fragment, InsuranceProductListData_ErgoLookupTable_Fragment } from './InsuranceProductListData';
import type { ErgoLookupTableSettingsDetailsFragment } from './ErgoLookupTableSettingDetails';
import type { LocalModelSpecsFragment } from './LocalModelSpecs';
import type { LocalMakeSpecsFragment } from './LocalMakeSpecs';
import type { AdvancedVersioningDataFragment } from './AdvancedVersioningData';
import type { DealerDisclaimersConfiguratorDataFragment } from './DealerPriceDisclaimerConfiguratorData';
import type { KycPresetsOptionsDataFragment } from './KYCPresetsOptionsData';
import type { DealershipSettingSpecData_DealershipMyInfoSetting_Fragment, DealershipSettingSpecData_DealershipPaymentSetting_Fragment, DealershipSettingSpecData_DealershipPublicSalesPerson_Fragment } from './DealershipSettingSpecData';
import type { FlexibleDiscountDataFragment } from './FlexibleDiscountData';
import type { CounterSettingsSpecsFragment } from './CounterSettingsSpecs';
import type { StandardApplicationModuleEmailContentsSpecsFragment, StandardApplicationModuleEmailContentCustomerSpecsFragment, StandardApplicationModuleEmailContentShareSubmissionSpecsFragment, StandardApplicationModuleEmailContentSpecsFragment, StandardApplicationModuleEmailContentSalesPersonSpecsFragment } from './StandardApplicationModuleEmailContentsSpecs';
import type { DealerTranslatedStringSettingDataFragment } from './DealerTranslatedStringData';
import type { DealerUploadedFileWithPreviewDataFragment } from './DealerUploadedFileWithPreview';
import type { UploadFileWithPreviewFormDataFragment } from './UploadFileWithPreviewFormData';
import type { DealerBooleanSettingDataFragment } from './DealerBooleanSettingData';
import type { EventApplicationModuleSpecsFragment } from './EventApplicationModuleSpecs';
import type { AppointmentModuleOnEventModuleDataFragment } from './AppointmentModuleOnEventModuleData';
import type { AppointmentTimeSlotDataFragment } from './AppointmentTimeSlotData';
import type { EventApplicationModuleEmailContentSpecsFragment, EventEmailContentSpecsFragment } from './EventApplicationModuleEmailContentSpecs';
import type { TranslatedTextDataFragment } from './TranslationTextData';
import type { AdyenPaymentModuleSpecsFragment } from './AdyenPaymentModuleSpecs';
import type { AdyenPaymentSettingsSpecFragment } from './AdyenPaymentSettingsSpec';
import type { PorschePaymentModuleSpecsFragment } from './PorschePaymentModuleSpecs';
import type { PorschePaymentSettingsSpecFragment } from './PorschePaymentSettingsSpec';
import type { FiservPaymentModuleSpecsFragment } from './FiservPaymentModuleSpecs';
import type { FiservPaymentSettingsSpecFragment } from './FiservPaymentSettingsSpec';
import type { PayGatePaymentModuleSpecsFragment } from './PayGatePaymentModuleSpecs';
import type { PayGatePaymentSettingsSpecFragment } from './PayGatePaymentSettingsSpec';
import type { TtbPaymentModuleSpecsFragment } from './TtbPaymentModuleSpecs';
import type { TtbPaymentSettingsSpecFragment } from './TtbPaymentSettingsSpec';
import type { MyInfoModuleSpecsFragment } from './MyInfoModuleSpecs';
import type { MyInfoSettingSpecFragment } from './MyInfoSettingSpec';
import type { ConfiguratorModuleSpecsFragment } from './ConfiguratorModuleSpecs';
import type { ConfiguratorModuleEmailContentSpecsFragment } from './ConfiguratorModuleEmailContentSpecs';
import type { WhatsappLiveChatModuleSpecsFragment } from './WhatsappLiveChatModuleSpecs';
import type { WhatsappLiveChatSettingsSpecFragment } from './WhatsappLiveChatSettingsSpec';
import type { UserlikeChatbotModuleSpecsFragment } from './UserlikeChatbotModuleSpecs';
import type { UserlikeChatbotSettingsSpecFragment } from './UserlikeChatbotSettingsSpec';
import type { PromoCodeModuleSpecsFragment } from './PromoCodeModuleSpecs';
import type { MaintenanceModuleSpecsFragment } from './MaintenanceModuleSpecs';
import type { WebsiteModuleSpecsFragment } from './WebsiteModuleSpecs';
import type { EdmSocialMediaDataFragment } from './EdmSocialMediaData';
import type { MobilityModuleSpecsFragment } from './MobilityModuleSpecs';
import type { DealerBookingCodeSpecsFragment } from './DealerBookingCodeSpecs';
import type { MobilitySigningSettingSpecsFragment } from './MobilitySigningSettingSpecs';
import type { MobilityModuleEmailScenarioContentSpecsFragment } from './MobilityModuleEmailScenarioContentSpecs';
import type { MobilityCustomerEmailContentDataFragment } from './MobilityCustomerEmailContentData';
import type { MobilityEmailContentDataFragment } from './MobilityEmailContentData';
import type { MobilityOperatorEmailContentDataFragment } from './MobilityOperatorEmailContentData';
import type { MobilityHomeDeliveryDataFragment } from './MobilityHomeDeliveryData';
import type { LabelsModuleSpecsFragment } from './LabelsModuleSpecs';
import type { FinderVehicleManagementModuleSpecsFragment } from './FinderVehicleManagementModuleSpecs';
import type { FinderApplicationPublicModuleSpecsFragment } from './FinderApplicationPublicModuleSpecs';
import type { FinderApplicationModuleEmailContentSpecsFragment } from './FinderApplicationModuleEmailContentSpecs';
import type { ModuleDisclaimersDataFragment } from './ModuleDisclaimersData';
import type { FinderApplicationPrivateModuleSpecsFragment } from './FinderApplicationPrivateModuleSpecs';
import type { AutoplayModuleSpecsFragment } from './AutoplayModuleSpecs';
import type { AutoplaySettingSpecsFragment } from './AutoplaySettingSpecs';
import type { CtsModuleSpecsFragment } from './CtsModuleSpecs';
import type { CtsModuleSettingDataFragment } from './CtsModuleSettingData';
import type { AppointmentModuleSpecsFragment } from './AppointmentModuleSpecs';
import type { AppointmentModuleEmailContentsSpecsFragment, AppointmentModuleEmailContentCustomerSpecsFragment, AppointmentModuleEmailContentSpecsFragment, AppointmentModuleEmailContentSalesPersonSpecsFragment, AppointmentModuleEmailContentFinderReservationSpecsFragment } from './AppointmentModuleEmailContentsSpecs';
import type { InsuranceModuleSpecsFragment } from './InsuranceModuleSpecs';
import type { PorscheMasterDataModuleSpecsFragment } from './PorscheMasterDataModuleSpecs';
import type { GiftVoucherModuleSpecsFragment } from './GiftVoucherModuleSpecs';
import type { GiftVoucherModuleEmailContentsSpecsFragment, GiftVoucherModuleEmailContentCustomerSpecsFragment, GiftVoucherModuleEmailDataFragment } from './GiftVoucherModuleEmailContentsSpecs';
import type { TradeInModuleSpecsFragment } from './TradeInModuleSpecs';
import type { TradeInSettingSpecFragment } from './TradeInSetting';
import type { CapModuleSpecsFragment } from './CapModuleSpecs';
import type { CapSettingSpecFragment } from './CapSettingSpec';
import type { PorscheIdModuleSpecsFragment } from './PorscheIdModuleSpecs';
import type { PorscheIdSettingSpecFragment } from './PorscheIdSettingSpec';
import type { PorscheRetainModuleSpecsFragment } from './PorscheRetainModuleSpecs';
import type { DocusignModuleSpecsFragment } from './DocusignModuleSpecs';
import type { DocusignSettingDataFragment } from './DocusignSettingData';
import type { LaunchPadModuleSpecsFragment } from './LaunchPadModuleSpecs';
import type { VisitAppointmentModuleSpecsFragment } from './VisitAppointmentModuleSpecs';
import type { TimeSlotDataFragment } from './TimeSlotData';
import type { VisitAppointmentModuleEmailContentsSpecsFragment, VisitAppointmentModuleEmailContentCustomerSpecsFragment, VisitAppointmentModuleEmailContentSpecsFragment, VisitAppointmentModuleEmailContentSalesPersonSpecsFragment } from './VisitAppointmentModuleEmailContentsSpecs';
import type { OidcModuleSpecsFragment } from './OIDCModuleSpecs';
import type { MarketingModuleSpecsFragment } from './MarketingModuleSpecs';
import type { SalesOfferModuleSpecsFragment } from './SalesOfferModuleSpecs';
import type { BankDetailsDataFragment } from './BankDetailsData';
import type { TranslatedStringSpecsFragment } from './TranslatedStringSpecs';
import type { BankIntegrationData_DbsBankIntegration_Fragment, BankIntegrationData_EmailBankIntegration_Fragment, BankIntegrationData_EnbdBankIntegration_Fragment, BankIntegrationData_HlfBankIntegration_Fragment, BankIntegrationData_HlfBankV2Integration_Fragment, BankIntegrationData_MaybankIntegration_Fragment, BankIntegrationData_UobBankIntegration_Fragment } from './BankIntegrationData';
import type { UploadFileFormDataFragment } from './UploadFileFormData';
import type { FinanceProductDetailsData_LocalDeferredPrincipal_Fragment, FinanceProductDetailsData_LocalHirePurchase_Fragment, FinanceProductDetailsData_LocalHirePurchaseWithBalloon_Fragment, FinanceProductDetailsData_LocalHirePurchaseWithBalloonGfv_Fragment, FinanceProductDetailsData_LocalLease_Fragment, FinanceProductDetailsData_LocalLeasePurchase_Fragment, FinanceProductDetailsData_LocalUcclLeasing_Fragment } from './FinanceProductDetailsData';
import type { PaymentSettingsDetailsFragment } from './PaymentSettingsDetails';
import type { LoanSettingsDetailsFragment } from './LoanSettingsDetails';
import type { TermSettingsDetails_DeferredPrincipalTermSettings_Fragment, TermSettingsDetails_GenericPrincipalTermSettings_Fragment } from './TermSettingsDetails';
import type { InterestRateSettingsDetails_InterestRateFixedSettings_Fragment, InterestRateSettingsDetails_InterestRateRangeSettings_Fragment, InterestRateSettingsDetails_InterestRateTableSettings_Fragment } from './InterestRateSettingsDetails';
import type { DownPaymentSettingsDetails_DownPaymentRangeSettings_Fragment, DownPaymentSettingsDetails_DownPaymentTableSettings_Fragment } from './DownPaymentSettingsDetails';
import type { LeaseSettingsDetailsFragment } from './LeaseSettingsDetails';
import type { DepositSettingsDetails_DepositRangeSettings_Fragment, DepositSettingsDetails_DepositTableSettings_Fragment } from './DepositSettingsDetails';
import type { ResidualValueSettingsDetailsFragment } from './ResidualValueSettingsDetails';
import type { LocalUcclLeasingOnlyDetailsFragment } from './LocalUcclLeasingOnlyDetails';
import type { SalesOfferModuleEmailContentsSpecsFragment, SalesOfferEmailContentsSpecsFragment } from './SalesOfferModuleEmailContentsSpecs';
import type { VehicleDataWithPorscheCodeIntegrationModuleSpecsFragment } from './VehicleDataWithPorscheCodeIntegrationModuleSpecs';
import type { SalesControlBoardModuleSpecsFragment } from './SalesControlBoardModuleSpecs';
import type { DealerIntDataFragment, DealerFloatDataFragment, DealerObjectIdDataFragment } from './DealerIntData';
import type { OfrModuleSpecsFragment } from './OFRModuleSpecs';
import type { OfrModuleEmailContentsSpecsFragment, OfrSalesConsultantEmailContentSpecsFragment, OfrSalesConsultantEmailContentContextSpecsFragment, OfrCustomerEmailContentSpecsFragment, OfrCustomerEmailContentContextSpecsFragment } from './OFRModuleEmailContentsSpecs';
import type { OfrEquityDataFragment } from './OFREquityData';
import { gql } from '@apollo/client';
import { ConsentsAndDeclarationsModuleSpecsFragmentDoc } from './ConsentsAndDeclarationsModuleSpecs';
import { SimpleVersioningDataFragmentDoc } from './SimpleVersioningData';
import { AuthorDataFragmentDoc } from './AuthorData';
import { SimpleVehicleManagementModuleSpecsFragmentDoc } from './SimpleVehicleManagementModuleSpecs';
import { CompanyInModuleOptionDataFragmentDoc } from './CompanyInModuleOptionData';
import { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc } from './VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import { LocalCustomerManagementModuleSpecsFragmentDoc } from './LocalCustomerManagementModuleSpecs';
import { LocalCustomerManagementModuleKycFieldSpecsFragmentDoc } from './LocalCustomerManagementModuleKycFieldSpecs';
import { KycExtraSettingsSpecsFragmentDoc } from './KYCExtraSettingsSpecs';
import { KycPresetsSpecFragmentDoc } from './KYCPresetsSpec';
import { ConditionSpecsFragmentDoc } from './ConditionSpecs';
import { BaseConditionSpecsFragmentDoc } from './BaseConditionSpecs';
import { MobilityLocationDataFragmentDoc } from './MobilityLocationData';
import { UserPreviewDataFragmentDoc } from './UserPreviewData';
import { BankModuleSpecsFragmentDoc } from './BankModuleSpecs';
import { BasicSigningModuleSpecsFragmentDoc } from './BasicSigningModuleSpecs';
import { NamirialSigningModuleSpecsFragmentDoc } from './NamirialSigningModuleSpecs';
import { NamirialSettingsSpecFragmentDoc } from './NamirialSettingsSpec';
import { StandardApplicationModuleSpecsFragmentDoc } from './StandardApplicationModuleSpecs';
import { DealerPriceDisclaimerDataFragmentDoc } from './DealerPriceDisclaimerData';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { DepositAmountDataFragmentDoc } from './DepositAmountData';
import { ApplicationMarketTypeFragmentFragmentDoc } from './ApplicationMarketTypeFragment';
import { DealerMarketDataFragmentDoc } from './DealerMarketData';
import { BankDealerMarketDataFragmentDoc } from './BankDealerMarketData';
import { NzFeesDealerMarketDataFragmentDoc } from './NzFeesDealerMarketData';
import { DealerVehiclesSpecsFragmentDoc } from './DealerVehiclesSpecs';
import { DealerFinanceProductsSpecsFragmentDoc } from './DealerFinanceProductsSpecs';
import { FinanceProductListDataFragmentDoc } from './FinanceProductListData';
import { PeriodDataFragmentDoc } from './PeriodData';
import { ModulesCompanyTimezoneDataFragmentDoc } from './ModulesCompanyTimezoneData';
import { VehicleReferenceParametersDataFragmentDoc } from './VehicleReferenceParametersData';
import { BalloonSettingsDetailsFragmentDoc } from './BalloonSettingsDetails';
import { BalloonGfvSettingsDetailsFragmentDoc } from './BalloonGFVSettingsDetails';
import { DealerInsuranceProductsSpecsFragmentDoc } from './DealerInsuranceProductsSpecs';
import { InsuranceProductListDataFragmentDoc } from './InsuranceProductListData';
import { ErgoLookupTableSettingsDetailsFragmentDoc } from './ErgoLookupTableSettingDetails';
import { LocalModelSpecsFragmentDoc } from './LocalModelSpecs';
import { LocalMakeSpecsFragmentDoc } from './LocalMakeSpecs';
import { AdvancedVersioningDataFragmentDoc } from './AdvancedVersioningData';
import { DealerDisclaimersConfiguratorDataFragmentDoc } from './DealerPriceDisclaimerConfiguratorData';
import { KycPresetsOptionsDataFragmentDoc } from './KYCPresetsOptionsData';
import { DealershipSettingSpecDataFragmentDoc } from './DealershipSettingSpecData';
import { FlexibleDiscountDataFragmentDoc } from './FlexibleDiscountData';
import { CounterSettingsSpecsFragmentDoc } from './CounterSettingsSpecs';
import { StandardApplicationModuleEmailContentsSpecsFragmentDoc, StandardApplicationModuleEmailContentCustomerSpecsFragmentDoc, StandardApplicationModuleEmailContentShareSubmissionSpecsFragmentDoc, StandardApplicationModuleEmailContentSpecsFragmentDoc, StandardApplicationModuleEmailContentSalesPersonSpecsFragmentDoc } from './StandardApplicationModuleEmailContentsSpecs';
import { DealerTranslatedStringSettingDataFragmentDoc } from './DealerTranslatedStringData';
import { DealerUploadedFileWithPreviewDataFragmentDoc } from './DealerUploadedFileWithPreview';
import { UploadFileWithPreviewFormDataFragmentDoc } from './UploadFileWithPreviewFormData';
import { DealerBooleanSettingDataFragmentDoc } from './DealerBooleanSettingData';
import { EventApplicationModuleSpecsFragmentDoc } from './EventApplicationModuleSpecs';
import { AppointmentModuleOnEventModuleDataFragmentDoc } from './AppointmentModuleOnEventModuleData';
import { AppointmentTimeSlotDataFragmentDoc } from './AppointmentTimeSlotData';
import { EventApplicationModuleEmailContentSpecsFragmentDoc, EventEmailContentSpecsFragmentDoc } from './EventApplicationModuleEmailContentSpecs';
import { TranslatedTextDataFragmentDoc } from './TranslationTextData';
import { AdyenPaymentModuleSpecsFragmentDoc } from './AdyenPaymentModuleSpecs';
import { AdyenPaymentSettingsSpecFragmentDoc } from './AdyenPaymentSettingsSpec';
import { PorschePaymentModuleSpecsFragmentDoc } from './PorschePaymentModuleSpecs';
import { PorschePaymentSettingsSpecFragmentDoc } from './PorschePaymentSettingsSpec';
import { FiservPaymentModuleSpecsFragmentDoc } from './FiservPaymentModuleSpecs';
import { FiservPaymentSettingsSpecFragmentDoc } from './FiservPaymentSettingsSpec';
import { PayGatePaymentModuleSpecsFragmentDoc } from './PayGatePaymentModuleSpecs';
import { PayGatePaymentSettingsSpecFragmentDoc } from './PayGatePaymentSettingsSpec';
import { TtbPaymentModuleSpecsFragmentDoc } from './TtbPaymentModuleSpecs';
import { TtbPaymentSettingsSpecFragmentDoc } from './TtbPaymentSettingsSpec';
import { MyInfoModuleSpecsFragmentDoc } from './MyInfoModuleSpecs';
import { MyInfoSettingSpecFragmentDoc } from './MyInfoSettingSpec';
import { ConfiguratorModuleSpecsFragmentDoc } from './ConfiguratorModuleSpecs';
import { ConfiguratorModuleEmailContentSpecsFragmentDoc } from './ConfiguratorModuleEmailContentSpecs';
import { WhatsappLiveChatModuleSpecsFragmentDoc } from './WhatsappLiveChatModuleSpecs';
import { WhatsappLiveChatSettingsSpecFragmentDoc } from './WhatsappLiveChatSettingsSpec';
import { UserlikeChatbotModuleSpecsFragmentDoc } from './UserlikeChatbotModuleSpecs';
import { UserlikeChatbotSettingsSpecFragmentDoc } from './UserlikeChatbotSettingsSpec';
import { PromoCodeModuleSpecsFragmentDoc } from './PromoCodeModuleSpecs';
import { MaintenanceModuleSpecsFragmentDoc } from './MaintenanceModuleSpecs';
import { WebsiteModuleSpecsFragmentDoc } from './WebsiteModuleSpecs';
import { EdmSocialMediaDataFragmentDoc } from './EdmSocialMediaData';
import { MobilityModuleSpecsFragmentDoc } from './MobilityModuleSpecs';
import { DealerBookingCodeSpecsFragmentDoc } from './DealerBookingCodeSpecs';
import { MobilitySigningSettingSpecsFragmentDoc } from './MobilitySigningSettingSpecs';
import { MobilityModuleEmailScenarioContentSpecsFragmentDoc } from './MobilityModuleEmailScenarioContentSpecs';
import { MobilityCustomerEmailContentDataFragmentDoc } from './MobilityCustomerEmailContentData';
import { MobilityEmailContentDataFragmentDoc } from './MobilityEmailContentData';
import { MobilityOperatorEmailContentDataFragmentDoc } from './MobilityOperatorEmailContentData';
import { MobilityHomeDeliveryDataFragmentDoc } from './MobilityHomeDeliveryData';
import { LabelsModuleSpecsFragmentDoc } from './LabelsModuleSpecs';
import { FinderVehicleManagementModuleSpecsFragmentDoc } from './FinderVehicleManagementModuleSpecs';
import { FinderApplicationPublicModuleSpecsFragmentDoc } from './FinderApplicationPublicModuleSpecs';
import { FinderApplicationModuleEmailContentSpecsFragmentDoc } from './FinderApplicationModuleEmailContentSpecs';
import { ModuleDisclaimersDataFragmentDoc } from './ModuleDisclaimersData';
import { FinderApplicationPrivateModuleSpecsFragmentDoc } from './FinderApplicationPrivateModuleSpecs';
import { AutoplayModuleSpecsFragmentDoc } from './AutoplayModuleSpecs';
import { AutoplaySettingSpecsFragmentDoc } from './AutoplaySettingSpecs';
import { CtsModuleSpecsFragmentDoc } from './CtsModuleSpecs';
import { CtsModuleSettingDataFragmentDoc } from './CtsModuleSettingData';
import { AppointmentModuleSpecsFragmentDoc } from './AppointmentModuleSpecs';
import { AppointmentModuleEmailContentsSpecsFragmentDoc, AppointmentModuleEmailContentCustomerSpecsFragmentDoc, AppointmentModuleEmailContentSpecsFragmentDoc, AppointmentModuleEmailContentSalesPersonSpecsFragmentDoc, AppointmentModuleEmailContentFinderReservationSpecsFragmentDoc } from './AppointmentModuleEmailContentsSpecs';
import { InsuranceModuleSpecsFragmentDoc } from './InsuranceModuleSpecs';
import { PorscheMasterDataModuleSpecsFragmentDoc } from './PorscheMasterDataModuleSpecs';
import { GiftVoucherModuleSpecsFragmentDoc } from './GiftVoucherModuleSpecs';
import { GiftVoucherModuleEmailContentsSpecsFragmentDoc, GiftVoucherModuleEmailContentCustomerSpecsFragmentDoc, GiftVoucherModuleEmailDataFragmentDoc } from './GiftVoucherModuleEmailContentsSpecs';
import { TradeInModuleSpecsFragmentDoc } from './TradeInModuleSpecs';
import { TradeInSettingSpecFragmentDoc } from './TradeInSetting';
import { CapModuleSpecsFragmentDoc } from './CapModuleSpecs';
import { CapSettingSpecFragmentDoc } from './CapSettingSpec';
import { PorscheIdModuleSpecsFragmentDoc } from './PorscheIdModuleSpecs';
import { PorscheIdSettingSpecFragmentDoc } from './PorscheIdSettingSpec';
import { PorscheRetainModuleSpecsFragmentDoc } from './PorscheRetainModuleSpecs';
import { DocusignModuleSpecsFragmentDoc } from './DocusignModuleSpecs';
import { DocusignSettingDataFragmentDoc } from './DocusignSettingData';
import { LaunchPadModuleSpecsFragmentDoc } from './LaunchPadModuleSpecs';
import { VisitAppointmentModuleSpecsFragmentDoc } from './VisitAppointmentModuleSpecs';
import { TimeSlotDataFragmentDoc } from './TimeSlotData';
import { VisitAppointmentModuleEmailContentsSpecsFragmentDoc, VisitAppointmentModuleEmailContentCustomerSpecsFragmentDoc, VisitAppointmentModuleEmailContentSpecsFragmentDoc, VisitAppointmentModuleEmailContentSalesPersonSpecsFragmentDoc } from './VisitAppointmentModuleEmailContentsSpecs';
import { OidcModuleSpecsFragmentDoc } from './OIDCModuleSpecs';
import { MarketingModuleSpecsFragmentDoc } from './MarketingModuleSpecs';
import { SalesOfferModuleSpecsFragmentDoc } from './SalesOfferModuleSpecs';
import { BankDetailsDataFragmentDoc } from './BankDetailsData';
import { TranslatedStringSpecsFragmentDoc } from './TranslatedStringSpecs';
import { BankIntegrationDataFragmentDoc } from './BankIntegrationData';
import { UploadFileFormDataFragmentDoc } from './UploadFileFormData';
import { FinanceProductDetailsDataFragmentDoc } from './FinanceProductDetailsData';
import { PaymentSettingsDetailsFragmentDoc } from './PaymentSettingsDetails';
import { LoanSettingsDetailsFragmentDoc } from './LoanSettingsDetails';
import { TermSettingsDetailsFragmentDoc } from './TermSettingsDetails';
import { InterestRateSettingsDetailsFragmentDoc } from './InterestRateSettingsDetails';
import { DownPaymentSettingsDetailsFragmentDoc } from './DownPaymentSettingsDetails';
import { LeaseSettingsDetailsFragmentDoc } from './LeaseSettingsDetails';
import { DepositSettingsDetailsFragmentDoc } from './DepositSettingsDetails';
import { ResidualValueSettingsDetailsFragmentDoc } from './ResidualValueSettingsDetails';
import { LocalUcclLeasingOnlyDetailsFragmentDoc } from './LocalUcclLeasingOnlyDetails';
import { SalesOfferModuleEmailContentsSpecsFragmentDoc, SalesOfferEmailContentsSpecsFragmentDoc } from './SalesOfferModuleEmailContentsSpecs';
import { VehicleDataWithPorscheCodeIntegrationModuleSpecsFragmentDoc } from './VehicleDataWithPorscheCodeIntegrationModuleSpecs';
import { SalesControlBoardModuleSpecsFragmentDoc } from './SalesControlBoardModuleSpecs';
import { DealerIntDataFragmentDoc, DealerFloatDataFragmentDoc, DealerObjectIdDataFragmentDoc } from './DealerIntData';
import { OfrModuleSpecsFragmentDoc } from './OFRModuleSpecs';
import { OfrModuleEmailContentsSpecsFragmentDoc, OfrSalesConsultantEmailContentSpecsFragmentDoc, OfrSalesConsultantEmailContentContextSpecsFragmentDoc, OfrCustomerEmailContentSpecsFragmentDoc, OfrCustomerEmailContentContextSpecsFragmentDoc } from './OFRModuleEmailContentsSpecs';
import { OfrEquityDataFragmentDoc } from './OFREquityData';
export type ModuleSpecs_AdyenPaymentModule_Fragment = (
  { __typename: 'AdyenPaymentModule' }
  & Pick<SchemaTypes.AdyenPaymentModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & AdyenPaymentModuleSpecsFragment
);

export type ModuleSpecs_AppointmentModule_Fragment = (
  { __typename: 'AppointmentModule' }
  & Pick<SchemaTypes.AppointmentModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & AppointmentModuleSpecsFragment
);

export type ModuleSpecs_AutoplayModule_Fragment = (
  { __typename: 'AutoplayModule' }
  & Pick<SchemaTypes.AutoplayModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & AutoplayModuleSpecsFragment
);

export type ModuleSpecs_BankModule_Fragment = (
  { __typename: 'BankModule' }
  & Pick<SchemaTypes.BankModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & BankModuleSpecsFragment
);

export type ModuleSpecs_BasicSigningModule_Fragment = (
  { __typename: 'BasicSigningModule' }
  & Pick<SchemaTypes.BasicSigningModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & BasicSigningModuleSpecsFragment
);

export type ModuleSpecs_CapModule_Fragment = (
  { __typename: 'CapModule' }
  & Pick<SchemaTypes.CapModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & CapModuleSpecsFragment
);

export type ModuleSpecs_ConfiguratorModule_Fragment = (
  { __typename: 'ConfiguratorModule' }
  & Pick<SchemaTypes.ConfiguratorModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & ConfiguratorModuleSpecsFragment
);

export type ModuleSpecs_ConsentsAndDeclarationsModule_Fragment = (
  { __typename: 'ConsentsAndDeclarationsModule' }
  & Pick<SchemaTypes.ConsentsAndDeclarationsModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & ConsentsAndDeclarationsModuleSpecsFragment
);

export type ModuleSpecs_CtsModule_Fragment = (
  { __typename: 'CtsModule' }
  & Pick<SchemaTypes.CtsModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & CtsModuleSpecsFragment
);

export type ModuleSpecs_DocusignModule_Fragment = (
  { __typename: 'DocusignModule' }
  & Pick<SchemaTypes.DocusignModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & DocusignModuleSpecsFragment
);

export type ModuleSpecs_EventApplicationModule_Fragment = (
  { __typename: 'EventApplicationModule' }
  & Pick<SchemaTypes.EventApplicationModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & EventApplicationModuleSpecsFragment
);

export type ModuleSpecs_FinderApplicationPrivateModule_Fragment = (
  { __typename: 'FinderApplicationPrivateModule' }
  & Pick<SchemaTypes.FinderApplicationPrivateModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & FinderApplicationPrivateModuleSpecsFragment
);

export type ModuleSpecs_FinderApplicationPublicModule_Fragment = (
  { __typename: 'FinderApplicationPublicModule' }
  & Pick<SchemaTypes.FinderApplicationPublicModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & FinderApplicationPublicModuleSpecsFragment
);

export type ModuleSpecs_FinderVehicleManagementModule_Fragment = (
  { __typename: 'FinderVehicleManagementModule' }
  & Pick<SchemaTypes.FinderVehicleManagementModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & FinderVehicleManagementModuleSpecsFragment
);

export type ModuleSpecs_FiservPaymentModule_Fragment = (
  { __typename: 'FiservPaymentModule' }
  & Pick<SchemaTypes.FiservPaymentModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & FiservPaymentModuleSpecsFragment
);

export type ModuleSpecs_GiftVoucherModule_Fragment = (
  { __typename: 'GiftVoucherModule' }
  & Pick<SchemaTypes.GiftVoucherModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & GiftVoucherModuleSpecsFragment
);

export type ModuleSpecs_InsuranceModule_Fragment = (
  { __typename: 'InsuranceModule' }
  & Pick<SchemaTypes.InsuranceModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & InsuranceModuleSpecsFragment
);

export type ModuleSpecs_LabelsModule_Fragment = (
  { __typename: 'LabelsModule' }
  & Pick<SchemaTypes.LabelsModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & LabelsModuleSpecsFragment
);

export type ModuleSpecs_LaunchPadModule_Fragment = (
  { __typename: 'LaunchPadModule' }
  & Pick<SchemaTypes.LaunchPadModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & LaunchPadModuleSpecsFragment
);

export type ModuleSpecs_LocalCustomerManagementModule_Fragment = (
  { __typename: 'LocalCustomerManagementModule' }
  & Pick<SchemaTypes.LocalCustomerManagementModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & LocalCustomerManagementModuleSpecsFragment
);

export type ModuleSpecs_MaintenanceModule_Fragment = (
  { __typename: 'MaintenanceModule' }
  & Pick<SchemaTypes.MaintenanceModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & MaintenanceModuleSpecsFragment
);

export type ModuleSpecs_MarketingModule_Fragment = (
  { __typename: 'MarketingModule' }
  & Pick<SchemaTypes.MarketingModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & MarketingModuleSpecsFragment
);

export type ModuleSpecs_MobilityModule_Fragment = (
  { __typename: 'MobilityModule' }
  & Pick<SchemaTypes.MobilityModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & MobilityModuleSpecsFragment
);

export type ModuleSpecs_MyInfoModule_Fragment = (
  { __typename: 'MyInfoModule' }
  & Pick<SchemaTypes.MyInfoModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & MyInfoModuleSpecsFragment
);

export type ModuleSpecs_NamirialSigningModule_Fragment = (
  { __typename: 'NamirialSigningModule' }
  & Pick<SchemaTypes.NamirialSigningModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & NamirialSigningModuleSpecsFragment
);

export type ModuleSpecs_OfrModule_Fragment = (
  { __typename: 'OFRModule' }
  & Pick<SchemaTypes.OfrModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & OfrModuleSpecsFragment
);

export type ModuleSpecs_OidcModule_Fragment = (
  { __typename: 'OIDCModule' }
  & Pick<SchemaTypes.OidcModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & OidcModuleSpecsFragment
);

export type ModuleSpecs_PayGatePaymentModule_Fragment = (
  { __typename: 'PayGatePaymentModule' }
  & Pick<SchemaTypes.PayGatePaymentModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & PayGatePaymentModuleSpecsFragment
);

export type ModuleSpecs_PorscheIdModule_Fragment = (
  { __typename: 'PorscheIdModule' }
  & Pick<SchemaTypes.PorscheIdModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & PorscheIdModuleSpecsFragment
);

export type ModuleSpecs_PorscheMasterDataModule_Fragment = (
  { __typename: 'PorscheMasterDataModule' }
  & Pick<SchemaTypes.PorscheMasterDataModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & PorscheMasterDataModuleSpecsFragment
);

export type ModuleSpecs_PorschePaymentModule_Fragment = (
  { __typename: 'PorschePaymentModule' }
  & Pick<SchemaTypes.PorschePaymentModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & PorschePaymentModuleSpecsFragment
);

export type ModuleSpecs_PorscheRetainModule_Fragment = (
  { __typename: 'PorscheRetainModule' }
  & Pick<SchemaTypes.PorscheRetainModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & PorscheRetainModuleSpecsFragment
);

export type ModuleSpecs_PromoCodeModule_Fragment = (
  { __typename: 'PromoCodeModule' }
  & Pick<SchemaTypes.PromoCodeModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & PromoCodeModuleSpecsFragment
);

export type ModuleSpecs_SalesControlBoardModule_Fragment = (
  { __typename: 'SalesControlBoardModule' }
  & Pick<SchemaTypes.SalesControlBoardModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & SalesControlBoardModuleSpecsFragment
);

export type ModuleSpecs_SalesOfferModule_Fragment = (
  { __typename: 'SalesOfferModule' }
  & Pick<SchemaTypes.SalesOfferModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & SalesOfferModuleSpecsFragment
);

export type ModuleSpecs_SimpleVehicleManagementModule_Fragment = (
  { __typename: 'SimpleVehicleManagementModule' }
  & Pick<SchemaTypes.SimpleVehicleManagementModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & SimpleVehicleManagementModuleSpecsFragment
);

export type ModuleSpecs_StandardApplicationModule_Fragment = (
  { __typename: 'StandardApplicationModule' }
  & Pick<SchemaTypes.StandardApplicationModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & StandardApplicationModuleSpecsFragment
);

export type ModuleSpecs_TradeInModule_Fragment = (
  { __typename: 'TradeInModule' }
  & Pick<SchemaTypes.TradeInModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & TradeInModuleSpecsFragment
);

export type ModuleSpecs_TtbPaymentModule_Fragment = (
  { __typename: 'TtbPaymentModule' }
  & Pick<SchemaTypes.TtbPaymentModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & TtbPaymentModuleSpecsFragment
);

export type ModuleSpecs_UserlikeChatbotModule_Fragment = (
  { __typename: 'UserlikeChatbotModule' }
  & Pick<SchemaTypes.UserlikeChatbotModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & UserlikeChatbotModuleSpecsFragment
);

export type ModuleSpecs_VehicleDataWithPorscheCodeIntegrationModule_Fragment = (
  { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
  & Pick<SchemaTypes.VehicleDataWithPorscheCodeIntegrationModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & VehicleDataWithPorscheCodeIntegrationModuleSpecsFragment
);

export type ModuleSpecs_VisitAppointmentModule_Fragment = (
  { __typename: 'VisitAppointmentModule' }
  & Pick<SchemaTypes.VisitAppointmentModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & VisitAppointmentModuleSpecsFragment
);

export type ModuleSpecs_WebsiteModule_Fragment = (
  { __typename: 'WebsiteModule' }
  & Pick<SchemaTypes.WebsiteModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & WebsiteModuleSpecsFragment
);

export type ModuleSpecs_WhatsappLiveChatModule_Fragment = (
  { __typename: 'WhatsappLiveChatModule' }
  & Pick<SchemaTypes.WhatsappLiveChatModule, 'id' | 'companyId'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
  ) }
  & WhatsappLiveChatModuleSpecsFragment
);

export type ModuleSpecsFragment = ModuleSpecs_AdyenPaymentModule_Fragment | ModuleSpecs_AppointmentModule_Fragment | ModuleSpecs_AutoplayModule_Fragment | ModuleSpecs_BankModule_Fragment | ModuleSpecs_BasicSigningModule_Fragment | ModuleSpecs_CapModule_Fragment | ModuleSpecs_ConfiguratorModule_Fragment | ModuleSpecs_ConsentsAndDeclarationsModule_Fragment | ModuleSpecs_CtsModule_Fragment | ModuleSpecs_DocusignModule_Fragment | ModuleSpecs_EventApplicationModule_Fragment | ModuleSpecs_FinderApplicationPrivateModule_Fragment | ModuleSpecs_FinderApplicationPublicModule_Fragment | ModuleSpecs_FinderVehicleManagementModule_Fragment | ModuleSpecs_FiservPaymentModule_Fragment | ModuleSpecs_GiftVoucherModule_Fragment | ModuleSpecs_InsuranceModule_Fragment | ModuleSpecs_LabelsModule_Fragment | ModuleSpecs_LaunchPadModule_Fragment | ModuleSpecs_LocalCustomerManagementModule_Fragment | ModuleSpecs_MaintenanceModule_Fragment | ModuleSpecs_MarketingModule_Fragment | ModuleSpecs_MobilityModule_Fragment | ModuleSpecs_MyInfoModule_Fragment | ModuleSpecs_NamirialSigningModule_Fragment | ModuleSpecs_OfrModule_Fragment | ModuleSpecs_OidcModule_Fragment | ModuleSpecs_PayGatePaymentModule_Fragment | ModuleSpecs_PorscheIdModule_Fragment | ModuleSpecs_PorscheMasterDataModule_Fragment | ModuleSpecs_PorschePaymentModule_Fragment | ModuleSpecs_PorscheRetainModule_Fragment | ModuleSpecs_PromoCodeModule_Fragment | ModuleSpecs_SalesControlBoardModule_Fragment | ModuleSpecs_SalesOfferModule_Fragment | ModuleSpecs_SimpleVehicleManagementModule_Fragment | ModuleSpecs_StandardApplicationModule_Fragment | ModuleSpecs_TradeInModule_Fragment | ModuleSpecs_TtbPaymentModule_Fragment | ModuleSpecs_UserlikeChatbotModule_Fragment | ModuleSpecs_VehicleDataWithPorscheCodeIntegrationModule_Fragment | ModuleSpecs_VisitAppointmentModule_Fragment | ModuleSpecs_WebsiteModule_Fragment | ModuleSpecs_WhatsappLiveChatModule_Fragment;

export const ModuleSpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment ModuleSpecs on Module {
  __typename
  id
  companyId
  company {
    id
    currency
    displayName
    timeZone
    countryCode
  }
  ... on ConsentsAndDeclarationsModule {
    ...ConsentsAndDeclarationsModuleSpecs
  }
  ... on SimpleVehicleManagementModule {
    ...SimpleVehicleManagementModuleSpecs
  }
  ... on LocalCustomerManagementModule {
    ...LocalCustomerManagementModuleSpecs
  }
  ... on BankModule {
    ...BankModuleSpecs
  }
  ... on BasicSigningModule {
    ...BasicSigningModuleSpecs
  }
  ... on NamirialSigningModule {
    ...NamirialSigningModuleSpecs
  }
  ... on StandardApplicationModule {
    ...StandardApplicationModuleSpecs
  }
  ... on EventApplicationModule {
    ...EventApplicationModuleSpecs
  }
  ... on AdyenPaymentModule {
    ...AdyenPaymentModuleSpecs
  }
  ... on PorschePaymentModule {
    ...PorschePaymentModuleSpecs
  }
  ... on FiservPaymentModule {
    ...FiservPaymentModuleSpecs
  }
  ... on PayGatePaymentModule {
    ...PayGatePaymentModuleSpecs
  }
  ... on TtbPaymentModule {
    ...TtbPaymentModuleSpecs
  }
  ... on MyInfoModule {
    ...MyInfoModuleSpecs
  }
  ... on ConfiguratorModule {
    ...ConfiguratorModuleSpecs
  }
  ... on WhatsappLiveChatModule {
    ...WhatsappLiveChatModuleSpecs
  }
  ... on UserlikeChatbotModule {
    ...UserlikeChatbotModuleSpecs
  }
  ... on PromoCodeModule {
    ...PromoCodeModuleSpecs
  }
  ... on MaintenanceModule {
    ...MaintenanceModuleSpecs
  }
  ... on WebsiteModule {
    ...WebsiteModuleSpecs
  }
  ... on MobilityModule {
    ...MobilityModuleSpecs
  }
  ... on LabelsModule {
    ...LabelsModuleSpecs
  }
  ... on FinderVehicleManagementModule {
    ...FinderVehicleManagementModuleSpecs
  }
  ... on FinderApplicationPublicModule {
    ...FinderApplicationPublicModuleSpecs
  }
  ... on FinderApplicationPrivateModule {
    ...FinderApplicationPrivateModuleSpecs
  }
  ... on AutoplayModule {
    ...AutoplayModuleSpecs
  }
  ... on CtsModule {
    ...CtsModuleSpecs
  }
  ... on AppointmentModule {
    ...AppointmentModuleSpecs
  }
  ... on InsuranceModule {
    ...InsuranceModuleSpecs
  }
  ... on PorscheMasterDataModule {
    ...PorscheMasterDataModuleSpecs
  }
  ... on GiftVoucherModule {
    ...GiftVoucherModuleSpecs
  }
  ... on TradeInModule {
    ...TradeInModuleSpecs
  }
  ... on CapModule {
    ...CapModuleSpecs
  }
  ... on PorscheIdModule {
    ...PorscheIdModuleSpecs
  }
  ... on PorscheRetainModule {
    ...PorscheRetainModuleSpecs
  }
  ... on DocusignModule {
    ...DocusignModuleSpecs
  }
  ... on LaunchPadModule {
    ...LaunchPadModuleSpecs
  }
  ... on VisitAppointmentModule {
    ...VisitAppointmentModuleSpecs
  }
  ... on OIDCModule {
    ...OIDCModuleSpecs
  }
  ... on MarketingModule {
    ...MarketingModuleSpecs
  }
  ... on SalesOfferModule {
    ...SalesOfferModuleSpecs
  }
  ... on VehicleDataWithPorscheCodeIntegrationModule {
    ...VehicleDataWithPorscheCodeIntegrationModuleSpecs
  }
  ... on SalesControlBoardModule {
    ...SalesControlBoardModuleSpecs
  }
  ... on OFRModule {
    ...OFRModuleSpecs
  }
}
    `;