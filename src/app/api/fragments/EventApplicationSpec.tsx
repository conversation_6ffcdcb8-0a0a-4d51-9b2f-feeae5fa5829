import type * as SchemaTypes from '../types';

import type { ApplicationAgreementData_CheckboxApplicationAgreement_Fragment, ApplicationAgreementData_GroupApplicationAgreement_Fragment, ApplicationAgreementData_MarketingApplicationAgreement_Fragment, ApplicationAgreementData_TextApplicationAgreement_Fragment } from './ApplicationAgreementData';
import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { ConditionSpecs_ApplicationModuleCondition_Fragment, ConditionSpecs_BankCondition_Fragment, ConditionSpecs_ContextualCondition_Fragment, ConditionSpecs_DealerCondition_Fragment, ConditionSpecs_GiftVoucherCondition_Fragment, ConditionSpecs_InsurerCondition_Fragment, ConditionSpecs_LocationCondition_Fragment, ConditionSpecs_LogicCondition_Fragment, ConditionSpecs_SalesOfferAgreementsCondition_Fragment } from './ConditionSpecs';
import type { BaseConditionSpecs_ApplicationModuleCondition_Fragment, BaseConditionSpecs_BankCondition_Fragment, BaseConditionSpecs_ContextualCondition_Fragment, BaseConditionSpecs_DealerCondition_Fragment, BaseConditionSpecs_GiftVoucherCondition_Fragment, BaseConditionSpecs_InsurerCondition_Fragment, BaseConditionSpecs_LocationCondition_Fragment, BaseConditionSpecs_LogicCondition_Fragment, BaseConditionSpecs_SalesOfferAgreementsCondition_Fragment } from './BaseConditionSpecs';
import type { MobilityLocationDataFragment } from './MobilityLocationData';
import type { UserPreviewDataFragment } from './UserPreviewData';
import type { MarketingPlatformSpecsFragment } from './MarketingPlatformSpecs';
import type { MarketingPlatformsAgreedSpecsFragment } from './MarketingPlatformsAgreedSpecs';
import type { UsersOptionsDataFragment } from './UsersOptionsData';
import type { DealerJourneyDataFragment } from './DealerJourneyData';
import type { ApplicationJourneyDeposit_ApplicationAdyenDeposit_Fragment, ApplicationJourneyDeposit_ApplicationFiservDeposit_Fragment, ApplicationJourneyDeposit_ApplicationPayGateDeposit_Fragment, ApplicationJourneyDeposit_ApplicationPorscheDeposit_Fragment, ApplicationJourneyDeposit_ApplicationTtbDeposit_Fragment } from './ApplicationJourneyDeposit';
import type { JourneyDraftFlowFragment } from './JourneyDraftFlow';
import type { ApplicationDocumentDataFragment } from './ApplicationDocumentData';
import type { CustomerSpecs_CorporateCustomer_Fragment, CustomerSpecs_Guarantor_Fragment, CustomerSpecs_LocalCustomer_Fragment } from './CustomerSpecs';
import type { LocalCustomerDataFragment } from './LocalCustomerData';
import type { LocalCustomerFieldData_LocalCustomerArrayStringField_Fragment, LocalCustomerFieldData_LocalCustomerDateField_Fragment, LocalCustomerFieldData_LocalCustomerDrivingLicenseField_Fragment, LocalCustomerFieldData_LocalCustomerNumberField_Fragment, LocalCustomerFieldData_LocalCustomerPhoneField_Fragment, LocalCustomerFieldData_LocalCustomerReferenceDetailSetField_Fragment, LocalCustomerFieldData_LocalCustomerSalaryTransferredBankSetField_Fragment, LocalCustomerFieldData_LocalCustomerStringDescriptionField_Fragment, LocalCustomerFieldData_LocalCustomerStringField_Fragment, LocalCustomerFieldData_LocalCustomerUaeIdentitySetField_Fragment, LocalCustomerFieldData_LocalCustomerUploadsField_Fragment, LocalCustomerFieldData_LocalCustomerVerifiedPhoneField_Fragment } from './LocalCustomerFieldData';
import type { CorporateCustomerDataFragment } from './CorporateCustomerData';
import type { GuarantorDataFragment } from './GuarantorData';
import type { KycFieldSpecsFragment } from './KYCFieldSpecs';
import type { ApplicationVariantSpecFragment } from './ApplicationVehicleSpec';
import type { UploadFileWithPreviewFormDataFragment } from './UploadFileWithPreviewFormData';
import type { ApplicationModelSpecsFragment } from './ApplicationModelSpec';
import type { LocalMakeSpecsFragment } from './LocalMakeSpecs';
import type { AdvancedVersioningDataFragment } from './AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import type { JourneyEventDataFragment } from './JourneyEventData';
import type { EventModuleData_AdyenPaymentModule_Fragment, EventModuleData_AppointmentModule_Fragment, EventModuleData_AutoplayModule_Fragment, EventModuleData_BankModule_Fragment, EventModuleData_BasicSigningModule_Fragment, EventModuleData_CapModule_Fragment, EventModuleData_ConfiguratorModule_Fragment, EventModuleData_ConsentsAndDeclarationsModule_Fragment, EventModuleData_CtsModule_Fragment, EventModuleData_DocusignModule_Fragment, EventModuleData_EventApplicationModule_Fragment, EventModuleData_FinderApplicationPrivateModule_Fragment, EventModuleData_FinderApplicationPublicModule_Fragment, EventModuleData_FinderVehicleManagementModule_Fragment, EventModuleData_FiservPaymentModule_Fragment, EventModuleData_GiftVoucherModule_Fragment, EventModuleData_InsuranceModule_Fragment, EventModuleData_LabelsModule_Fragment, EventModuleData_LaunchPadModule_Fragment, EventModuleData_LocalCustomerManagementModule_Fragment, EventModuleData_MaintenanceModule_Fragment, EventModuleData_MarketingModule_Fragment, EventModuleData_MobilityModule_Fragment, EventModuleData_MyInfoModule_Fragment, EventModuleData_NamirialSigningModule_Fragment, EventModuleData_OfrModule_Fragment, EventModuleData_OidcModule_Fragment, EventModuleData_PayGatePaymentModule_Fragment, EventModuleData_PorscheIdModule_Fragment, EventModuleData_PorscheMasterDataModule_Fragment, EventModuleData_PorschePaymentModule_Fragment, EventModuleData_PorscheRetainModule_Fragment, EventModuleData_PromoCodeModule_Fragment, EventModuleData_SalesControlBoardModule_Fragment, EventModuleData_SalesOfferModule_Fragment, EventModuleData_SimpleVehicleManagementModule_Fragment, EventModuleData_StandardApplicationModule_Fragment, EventModuleData_TradeInModule_Fragment, EventModuleData_TtbPaymentModule_Fragment, EventModuleData_UserlikeChatbotModule_Fragment, EventModuleData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, EventModuleData_VisitAppointmentModule_Fragment, EventModuleData_WebsiteModule_Fragment, EventModuleData_WhatsappLiveChatModule_Fragment } from './EventModuleData';
import type { CompanyInModuleOptionDataFragment } from './CompanyInModuleOptionData';
import type { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragment } from './VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import type { LocalCustomerManagementModuleKycFieldSpecsFragment } from './LocalCustomerManagementModuleKycFieldSpecs';
import type { AppointmentModuleOnEventModuleDataFragment } from './AppointmentModuleOnEventModuleData';
import type { AppointmentTimeSlotDataFragment } from './AppointmentTimeSlotData';
import type { TimeSlotDataFragment } from './TimeSlotData';
import type { EventApplicationModuleEmailContentSpecsFragment, EventEmailContentSpecsFragment } from './EventApplicationModuleEmailContentSpecs';
import type { TranslatedTextDataFragment } from './TranslationTextData';
import type { TranslatedStringSpecsFragment } from './TranslatedStringSpecs';
import type { DealershipSettingSpecData_DealershipMyInfoSetting_Fragment, DealershipSettingSpecData_DealershipPaymentSetting_Fragment, DealershipSettingSpecData_DealershipPublicSalesPerson_Fragment } from './DealershipSettingSpecData';
import type { DepositAmountDataFragment } from './DepositAmountData';
import type { SimpleVersioningDataFragment } from './SimpleVersioningData';
import type { KycPresetsSpecFragment } from './KYCPresetsSpec';
import type { KycExtraSettingsSpecsFragment } from './KYCExtraSettingsSpecs';
import type { CustomizedFieldDataFragment } from './CustomizedFieldData';
import type { AppointmentModuleEmailContentSpecsFragment } from './AppointmentModuleEmailContentsSpecs';
import type { DealerTranslatedStringSettingDataFragment } from './DealerTranslatedStringData';
import type { DealerBooleanSettingDataFragment } from './DealerBooleanSettingData';
import type { ThankYouPageContentSpecsFragment } from './ThankYouPageContent';
import type { CustomTestDriveBookingSlotsDataFragment } from './CustomTestDriveBookingSlotsData';
import type { TestDriveFixedPeriodDataFragment } from './TestDriveFixedPeriodData';
import type { TestDriveBookingWindowSettingsDataFragment } from './TestDriveBookingWindowSettingsData';
import type { TradeInVehicleDataFragment } from './TradeInVehicleData';
import type { NamirialSigningDataFragment } from './NamirialSigningData';
import type { EventApplicationModuleDebugJourneyFragment } from './EventApplicationModuleDebugJourney';
import type { AppointmentModuleApplicationJourneyFragment } from './AppointmentModuleApplicationJourney';
import type { NamirialSigningModuleSpecsFragment } from './NamirialSigningModuleSpecs';
import type { NamirialSettingsSpecFragment } from './NamirialSettingsSpec';
import type { VisitAppointmentModuleApplicationJourneyFragment } from './VisitAppointmentModuleApplicationJourney';
import type { ApplicationEventCustomizedFieldDataFragment } from './ApplicationEventCustomizedFieldData';
import type { LeadData_ConfiguratorLead_Fragment, LeadData_EventLead_Fragment, LeadData_FinderLead_Fragment, LeadData_LaunchpadLead_Fragment, LeadData_MobilityLead_Fragment, LeadData_StandardLead_Fragment } from './LeadData';
import type { StandardLeadDataFragment } from './StandardLeadData';
import type { VehicleSpecs_FinderVehicle_Fragment, VehicleSpecs_LocalMake_Fragment, VehicleSpecs_LocalModel_Fragment, VehicleSpecs_LocalVariant_Fragment } from './VehicleSpecs';
import type { LocalVariantBaseSpecsFragment } from './LocalVariantBaseSpecs';
import type { LocalModelSpecsFragment } from './LocalModelSpecs';
import type { FinderVehicleSpecsFragment } from './FinderVehicleSpecs';
import type { FullListingValueFragment, FormattedDateDataFragment, LocalizedStringDataFragment, LocalizedValueDataFragment, NumberUnitDataFragment } from './finderListing.fragment';
import type { FinderLeadDataFragment } from './FinderLeadData';
import type { EventLeadDataFragment } from './EventLeadData';
import type { LaunchpadLeadDataFragment } from './LaunchpadLeadData';
import type { ConfiguratorLeadDataFragment } from './ConfiguratorLeadData';
import type { ConfiguratorJourneyBlocksData_ApplicationConfiguratorColorSetting_Fragment, ConfiguratorJourneyBlocksData_ApplicationConfiguratorOptionSetting_Fragment, ConfiguratorJourneyBlocksData_ApplicationConfiguratorPackageSetting_Fragment, ConfiguratorJourneyBlocksData_ApplicationConfiguratorTrimSetting_Fragment } from './ConfiguratorJourneyBlocksData';
import type { BlockDetails_ColorBlock_Fragment, BlockDetails_OptionsBlock_Fragment, BlockDetails_PackageBlock_Fragment, BlockDetails_TrimBlock_Fragment } from './BlockDetails';
import type { OptionSettingDetails_ComboOptionSettings_Fragment, OptionSettingDetails_DropdownOptionSettings_Fragment, OptionSettingDetails_MultiSelectOptionSettings_Fragment, OptionSettingDetails_SingleSelectOptionSettings_Fragment } from './OptionSettingDetails';
import type { MobilityLeadDataFragment } from './MobilityLeadData';
import type { LaunchpadModuleSpecsForApplicationFragment } from './LaunchpadModuleSpecsForApplication';
import type { DealerVehiclesSpecsFragment } from './DealerVehiclesSpecs';
import type { DealerApplicationFragmentFragment } from './DealerApplicationFragment';
import type { DealerContactFragmentFragment } from './DealerContactFragment';
import type { DealerSocialMediaFragmentFragment } from './DealerSocialMediaFragment';
import type { ReferenceApplicationData_ConfiguratorApplication_Fragment, ReferenceApplicationData_EventApplication_Fragment, ReferenceApplicationData_FinderApplication_Fragment, ReferenceApplicationData_LaunchpadApplication_Fragment, ReferenceApplicationData_MobilityApplication_Fragment, ReferenceApplicationData_SalesOfferApplication_Fragment, ReferenceApplicationData_StandardApplication_Fragment, ReferenceDepositData_ApplicationAdyenDeposit_Fragment, ReferenceDepositData_ApplicationFiservDeposit_Fragment, ReferenceDepositData_ApplicationPayGateDeposit_Fragment, ReferenceDepositData_ApplicationPorscheDeposit_Fragment, ReferenceDepositData_ApplicationTtbDeposit_Fragment, ReferenceFinancingData_DefaultApplicationFinancing_Fragment, ReferenceFinancingData_NewZealandApplicationFinancing_Fragment, ReferenceFinancingData_SingaporeApplicationFinancing_Fragment, ReferenceInsuranceData_DefaultApplicationInsurancing_Fragment, ReferenceInsuranceData_NewZealandApplicationInsurancing_Fragment, ReferenceInsuranceData_SingaporeApplicationInsurancing_Fragment } from './ReferenceApplicationData';
import type { ApplicationStageData_ConfiguratorApplication_Fragment, ApplicationStageData_EventApplication_Fragment, ApplicationStageData_FinderApplication_Fragment, ApplicationStageData_LaunchpadApplication_Fragment, ApplicationStageData_MobilityApplication_Fragment, ApplicationStageData_SalesOfferApplication_Fragment, ApplicationStageData_StandardApplication_Fragment } from './ApplicationStageData';
import type { SalesOfferSpecsFragment } from './SalesOfferSpecs';
import type { VehicleSalesOfferSpecsFragment } from './VehicleSalesOfferSpecs';
import type { LocalVariantSpecsFragment } from './LocalVariantSpecs';
import type { PorscheVehicleDataSpecsFragment, PorscheVehicleDataFeatureSpecsFragment, PorscheVehicleImagesSpecsFragment } from './PorscheVehicleDataSpecs';
import type { LocalFittedOptionsSpecsFragment } from './LocalFittedOptionsSpecs';
import type { SalesOfferDocumentDataFragment } from './SalesOfferDocumentData';
import type { MainDetailsSalesOfferSpecsFragment } from './MainDetailsSalesOfferSpecs';
import type { TradeInSalesOfferSpecsFragment } from './TradeInSalesOfferSpecs';
import type { FinanceSalesOfferSpecsFragment } from './FinanceSalesOfferSpecs';
import type { ApplicationFinancingData_DefaultApplicationFinancing_Fragment, ApplicationFinancingData_NewZealandApplicationFinancing_Fragment, ApplicationFinancingData_SingaporeApplicationFinancing_Fragment } from './ApplicationFinancingData';
import type { InsuranceSalesOfferSpecsFragment } from './InsuranceSalesOfferSpecs';
import type { ApplicationInsurancingData_DefaultApplicationInsurancing_Fragment, ApplicationInsurancingData_NewZealandApplicationInsurancing_Fragment, ApplicationInsurancingData_SingaporeApplicationInsurancing_Fragment } from './ApplicationInsurancingData';
import type { DepositSalesOfferSpecsFragment } from './DepositSalesOfferSpecs';
import type { VsaSalesOfferSpecsFragment } from './VSASalesOfferSpecs';
import type { SalesOfferModuleSpecsFragment } from './SalesOfferModuleSpecs';
import type { ApplicationMarketTypeFragment_DefaultApplicationMarket_Fragment, ApplicationMarketTypeFragment_NewZealandApplicationMarket_Fragment, ApplicationMarketTypeFragment_SingaporeApplicationMarket_Fragment } from './ApplicationMarketTypeFragment';
import type { DealerMarketDataFragment } from './DealerMarketData';
import type { BankDealerMarketDataFragment } from './BankDealerMarketData';
import type { NzFeesDealerMarketDataFragment } from './NzFeesDealerMarketData';
import type { DealerDisclaimersConfiguratorDataFragment } from './DealerPriceDisclaimerConfiguratorData';
import type { BankDetailsDataFragment } from './BankDetailsData';
import type { BankIntegrationData_DbsBankIntegration_Fragment, BankIntegrationData_EmailBankIntegration_Fragment, BankIntegrationData_EnbdBankIntegration_Fragment, BankIntegrationData_HlfBankIntegration_Fragment, BankIntegrationData_HlfBankV2Integration_Fragment, BankIntegrationData_MaybankIntegration_Fragment, BankIntegrationData_UobBankIntegration_Fragment } from './BankIntegrationData';
import type { UploadFileFormDataFragment } from './UploadFileFormData';
import type { FinanceProductDetailsData_LocalDeferredPrincipal_Fragment, FinanceProductDetailsData_LocalHirePurchase_Fragment, FinanceProductDetailsData_LocalHirePurchaseWithBalloon_Fragment, FinanceProductDetailsData_LocalHirePurchaseWithBalloonGfv_Fragment, FinanceProductDetailsData_LocalLease_Fragment, FinanceProductDetailsData_LocalLeasePurchase_Fragment, FinanceProductDetailsData_LocalUcclLeasing_Fragment } from './FinanceProductDetailsData';
import type { ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment, ModulesCompanyTimezoneData_AppointmentModule_Fragment, ModulesCompanyTimezoneData_AutoplayModule_Fragment, ModulesCompanyTimezoneData_BankModule_Fragment, ModulesCompanyTimezoneData_BasicSigningModule_Fragment, ModulesCompanyTimezoneData_CapModule_Fragment, ModulesCompanyTimezoneData_ConfiguratorModule_Fragment, ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment, ModulesCompanyTimezoneData_CtsModule_Fragment, ModulesCompanyTimezoneData_DocusignModule_Fragment, ModulesCompanyTimezoneData_EventApplicationModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment, ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_FiservPaymentModule_Fragment, ModulesCompanyTimezoneData_GiftVoucherModule_Fragment, ModulesCompanyTimezoneData_InsuranceModule_Fragment, ModulesCompanyTimezoneData_LabelsModule_Fragment, ModulesCompanyTimezoneData_LaunchPadModule_Fragment, ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment, ModulesCompanyTimezoneData_MaintenanceModule_Fragment, ModulesCompanyTimezoneData_MarketingModule_Fragment, ModulesCompanyTimezoneData_MobilityModule_Fragment, ModulesCompanyTimezoneData_MyInfoModule_Fragment, ModulesCompanyTimezoneData_NamirialSigningModule_Fragment, ModulesCompanyTimezoneData_OfrModule_Fragment, ModulesCompanyTimezoneData_OidcModule_Fragment, ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheIdModule_Fragment, ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment, ModulesCompanyTimezoneData_PorschePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheRetainModule_Fragment, ModulesCompanyTimezoneData_PromoCodeModule_Fragment, ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment, ModulesCompanyTimezoneData_SalesOfferModule_Fragment, ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_StandardApplicationModule_Fragment, ModulesCompanyTimezoneData_TradeInModule_Fragment, ModulesCompanyTimezoneData_TtbPaymentModule_Fragment, ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment, ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment, ModulesCompanyTimezoneData_WebsiteModule_Fragment, ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment } from './ModulesCompanyTimezoneData';
import type { PeriodDataFragment } from './PeriodData';
import type { VehicleReferenceParametersDataFragment } from './VehicleReferenceParametersData';
import type { PaymentSettingsDetailsFragment } from './PaymentSettingsDetails';
import type { LoanSettingsDetailsFragment } from './LoanSettingsDetails';
import type { TermSettingsDetails_DeferredPrincipalTermSettings_Fragment, TermSettingsDetails_GenericPrincipalTermSettings_Fragment } from './TermSettingsDetails';
import type { InterestRateSettingsDetails_InterestRateFixedSettings_Fragment, InterestRateSettingsDetails_InterestRateRangeSettings_Fragment, InterestRateSettingsDetails_InterestRateTableSettings_Fragment } from './InterestRateSettingsDetails';
import type { DownPaymentSettingsDetails_DownPaymentRangeSettings_Fragment, DownPaymentSettingsDetails_DownPaymentTableSettings_Fragment } from './DownPaymentSettingsDetails';
import type { BalloonSettingsDetails_BalloonRangeSettings_Fragment, BalloonSettingsDetails_BalloonTableSettings_Fragment } from './BalloonSettingsDetails';
import type { BalloonGfvSettingsDetailsFragment } from './BalloonGFVSettingsDetails';
import type { LeaseSettingsDetailsFragment } from './LeaseSettingsDetails';
import type { DepositSettingsDetails_DepositRangeSettings_Fragment, DepositSettingsDetails_DepositTableSettings_Fragment } from './DepositSettingsDetails';
import type { ResidualValueSettingsDetailsFragment } from './ResidualValueSettingsDetails';
import type { LocalUcclLeasingOnlyDetailsFragment } from './LocalUcclLeasingOnlyDetails';
import type { CounterSettingsSpecsFragment } from './CounterSettingsSpecs';
import type { DealerFinanceProductsSpecsFragment } from './DealerFinanceProductsSpecs';
import type { FinanceProductListData_LocalDeferredPrincipal_Fragment, FinanceProductListData_LocalHirePurchase_Fragment, FinanceProductListData_LocalHirePurchaseWithBalloon_Fragment, FinanceProductListData_LocalHirePurchaseWithBalloonGfv_Fragment, FinanceProductListData_LocalLease_Fragment, FinanceProductListData_LocalLeasePurchase_Fragment, FinanceProductListData_LocalUcclLeasing_Fragment } from './FinanceProductListData';
import type { DealerInsuranceProductsSpecsFragment } from './DealerInsuranceProductsSpecs';
import type { InsuranceProductListData_Eazy_Fragment, InsuranceProductListData_ErgoLookupTable_Fragment } from './InsuranceProductListData';
import type { ErgoLookupTableSettingsDetailsFragment } from './ErgoLookupTableSettingDetails';
import type { SalesOfferModuleEmailContentsSpecsFragment, SalesOfferEmailContentsSpecsFragment } from './SalesOfferModuleEmailContentsSpecs';
import type { SalesOfferKycPresetSpecsFragment } from './SalesOfferKYCPresetSpecs';
import type { SalesOfferConsentsSpecsFragment } from './SalesOfferConsentsSpecs';
import type { SalesOfferSigningsSpecsFragment } from './SalesOfferSigningsSpecs';
import { gql } from '@apollo/client';
import { ApplicationAgreementDataFragmentDoc } from './ApplicationAgreementData';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { ConditionSpecsFragmentDoc } from './ConditionSpecs';
import { BaseConditionSpecsFragmentDoc } from './BaseConditionSpecs';
import { MobilityLocationDataFragmentDoc } from './MobilityLocationData';
import { UserPreviewDataFragmentDoc } from './UserPreviewData';
import { MarketingPlatformSpecsFragmentDoc } from './MarketingPlatformSpecs';
import { MarketingPlatformsAgreedSpecsFragmentDoc } from './MarketingPlatformsAgreedSpecs';
import { UsersOptionsDataFragmentDoc } from './UsersOptionsData';
import { DealerJourneyDataFragmentDoc } from './DealerJourneyData';
import { ApplicationJourneyDepositFragmentDoc } from './ApplicationJourneyDeposit';
import { JourneyDraftFlowFragmentDoc } from './JourneyDraftFlow';
import { ApplicationDocumentDataFragmentDoc } from './ApplicationDocumentData';
import { CustomerSpecsFragmentDoc } from './CustomerSpecs';
import { LocalCustomerDataFragmentDoc } from './LocalCustomerData';
import { LocalCustomerFieldDataFragmentDoc } from './LocalCustomerFieldData';
import { CorporateCustomerDataFragmentDoc } from './CorporateCustomerData';
import { GuarantorDataFragmentDoc } from './GuarantorData';
import { KycFieldSpecsFragmentDoc } from './KYCFieldSpecs';
import { ApplicationVariantSpecFragmentDoc } from './ApplicationVehicleSpec';
import { UploadFileWithPreviewFormDataFragmentDoc } from './UploadFileWithPreviewFormData';
import { ApplicationModelSpecsFragmentDoc } from './ApplicationModelSpec';
import { LocalMakeSpecsFragmentDoc } from './LocalMakeSpecs';
import { AdvancedVersioningDataFragmentDoc } from './AdvancedVersioningData';
import { AuthorDataFragmentDoc } from './AuthorData';
import { JourneyEventDataFragmentDoc } from './JourneyEventData';
import { EventModuleDataFragmentDoc } from './EventModuleData';
import { CompanyInModuleOptionDataFragmentDoc } from './CompanyInModuleOptionData';
import { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc } from './VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import { LocalCustomerManagementModuleKycFieldSpecsFragmentDoc } from './LocalCustomerManagementModuleKycFieldSpecs';
import { AppointmentModuleOnEventModuleDataFragmentDoc } from './AppointmentModuleOnEventModuleData';
import { AppointmentTimeSlotDataFragmentDoc } from './AppointmentTimeSlotData';
import { TimeSlotDataFragmentDoc } from './TimeSlotData';
import { EventApplicationModuleEmailContentSpecsFragmentDoc, EventEmailContentSpecsFragmentDoc } from './EventApplicationModuleEmailContentSpecs';
import { TranslatedTextDataFragmentDoc } from './TranslationTextData';
import { TranslatedStringSpecsFragmentDoc } from './TranslatedStringSpecs';
import { DealershipSettingSpecDataFragmentDoc } from './DealershipSettingSpecData';
import { DepositAmountDataFragmentDoc } from './DepositAmountData';
import { SimpleVersioningDataFragmentDoc } from './SimpleVersioningData';
import { KycPresetsSpecFragmentDoc } from './KYCPresetsSpec';
import { KycExtraSettingsSpecsFragmentDoc } from './KYCExtraSettingsSpecs';
import { CustomizedFieldDataFragmentDoc } from './CustomizedFieldData';
import { AppointmentModuleEmailContentSpecsFragmentDoc } from './AppointmentModuleEmailContentsSpecs';
import { DealerTranslatedStringSettingDataFragmentDoc } from './DealerTranslatedStringData';
import { DealerBooleanSettingDataFragmentDoc } from './DealerBooleanSettingData';
import { ThankYouPageContentSpecsFragmentDoc } from './ThankYouPageContent';
import { CustomTestDriveBookingSlotsDataFragmentDoc } from './CustomTestDriveBookingSlotsData';
import { TestDriveFixedPeriodDataFragmentDoc } from './TestDriveFixedPeriodData';
import { TestDriveBookingWindowSettingsDataFragmentDoc } from './TestDriveBookingWindowSettingsData';
import { TradeInVehicleDataFragmentDoc } from './TradeInVehicleData';
import { NamirialSigningDataFragmentDoc } from './NamirialSigningData';
import { EventApplicationModuleDebugJourneyFragmentDoc } from './EventApplicationModuleDebugJourney';
import { AppointmentModuleApplicationJourneyFragmentDoc } from './AppointmentModuleApplicationJourney';
import { NamirialSigningModuleSpecsFragmentDoc } from './NamirialSigningModuleSpecs';
import { NamirialSettingsSpecFragmentDoc } from './NamirialSettingsSpec';
import { VisitAppointmentModuleApplicationJourneyFragmentDoc } from './VisitAppointmentModuleApplicationJourney';
import { ApplicationEventCustomizedFieldDataFragmentDoc } from './ApplicationEventCustomizedFieldData';
import { LeadDataFragmentDoc } from './LeadData';
import { StandardLeadDataFragmentDoc } from './StandardLeadData';
import { VehicleSpecsFragmentDoc } from './VehicleSpecs';
import { LocalVariantBaseSpecsFragmentDoc } from './LocalVariantBaseSpecs';
import { LocalModelSpecsFragmentDoc } from './LocalModelSpecs';
import { FinderVehicleSpecsFragmentDoc } from './FinderVehicleSpecs';
import { FullListingValueFragmentDoc, FormattedDateDataFragmentDoc, LocalizedStringDataFragmentDoc, LocalizedValueDataFragmentDoc, NumberUnitDataFragmentDoc } from './finderListing.fragment';
import { FinderLeadDataFragmentDoc } from './FinderLeadData';
import { EventLeadDataFragmentDoc } from './EventLeadData';
import { LaunchpadLeadDataFragmentDoc } from './LaunchpadLeadData';
import { ConfiguratorLeadDataFragmentDoc } from './ConfiguratorLeadData';
import { ConfiguratorJourneyBlocksDataFragmentDoc } from './ConfiguratorJourneyBlocksData';
import { BlockDetailsFragmentDoc } from './BlockDetails';
import { OptionSettingDetailsFragmentDoc } from './OptionSettingDetails';
import { MobilityLeadDataFragmentDoc } from './MobilityLeadData';
import { LaunchpadModuleSpecsForApplicationFragmentDoc } from './LaunchpadModuleSpecsForApplication';
import { DealerVehiclesSpecsFragmentDoc } from './DealerVehiclesSpecs';
import { DealerApplicationFragmentFragmentDoc } from './DealerApplicationFragment';
import { DealerContactFragmentFragmentDoc } from './DealerContactFragment';
import { DealerSocialMediaFragmentFragmentDoc } from './DealerSocialMediaFragment';
import { ReferenceApplicationDataFragmentDoc, ReferenceDepositDataFragmentDoc, ReferenceFinancingDataFragmentDoc, ReferenceInsuranceDataFragmentDoc } from './ReferenceApplicationData';
import { ApplicationStageDataFragmentDoc } from './ApplicationStageData';
import { SalesOfferSpecsFragmentDoc } from './SalesOfferSpecs';
import { VehicleSalesOfferSpecsFragmentDoc } from './VehicleSalesOfferSpecs';
import { LocalVariantSpecsFragmentDoc } from './LocalVariantSpecs';
import { PorscheVehicleDataSpecsFragmentDoc, PorscheVehicleDataFeatureSpecsFragmentDoc, PorscheVehicleImagesSpecsFragmentDoc } from './PorscheVehicleDataSpecs';
import { LocalFittedOptionsSpecsFragmentDoc } from './LocalFittedOptionsSpecs';
import { SalesOfferDocumentDataFragmentDoc } from './SalesOfferDocumentData';
import { MainDetailsSalesOfferSpecsFragmentDoc } from './MainDetailsSalesOfferSpecs';
import { TradeInSalesOfferSpecsFragmentDoc } from './TradeInSalesOfferSpecs';
import { FinanceSalesOfferSpecsFragmentDoc } from './FinanceSalesOfferSpecs';
import { ApplicationFinancingDataFragmentDoc } from './ApplicationFinancingData';
import { InsuranceSalesOfferSpecsFragmentDoc } from './InsuranceSalesOfferSpecs';
import { ApplicationInsurancingDataFragmentDoc } from './ApplicationInsurancingData';
import { DepositSalesOfferSpecsFragmentDoc } from './DepositSalesOfferSpecs';
import { VsaSalesOfferSpecsFragmentDoc } from './VSASalesOfferSpecs';
import { SalesOfferModuleSpecsFragmentDoc } from './SalesOfferModuleSpecs';
import { ApplicationMarketTypeFragmentFragmentDoc } from './ApplicationMarketTypeFragment';
import { DealerMarketDataFragmentDoc } from './DealerMarketData';
import { BankDealerMarketDataFragmentDoc } from './BankDealerMarketData';
import { NzFeesDealerMarketDataFragmentDoc } from './NzFeesDealerMarketData';
import { DealerDisclaimersConfiguratorDataFragmentDoc } from './DealerPriceDisclaimerConfiguratorData';
import { BankDetailsDataFragmentDoc } from './BankDetailsData';
import { BankIntegrationDataFragmentDoc } from './BankIntegrationData';
import { UploadFileFormDataFragmentDoc } from './UploadFileFormData';
import { FinanceProductDetailsDataFragmentDoc } from './FinanceProductDetailsData';
import { ModulesCompanyTimezoneDataFragmentDoc } from './ModulesCompanyTimezoneData';
import { PeriodDataFragmentDoc } from './PeriodData';
import { VehicleReferenceParametersDataFragmentDoc } from './VehicleReferenceParametersData';
import { PaymentSettingsDetailsFragmentDoc } from './PaymentSettingsDetails';
import { LoanSettingsDetailsFragmentDoc } from './LoanSettingsDetails';
import { TermSettingsDetailsFragmentDoc } from './TermSettingsDetails';
import { InterestRateSettingsDetailsFragmentDoc } from './InterestRateSettingsDetails';
import { DownPaymentSettingsDetailsFragmentDoc } from './DownPaymentSettingsDetails';
import { BalloonSettingsDetailsFragmentDoc } from './BalloonSettingsDetails';
import { BalloonGfvSettingsDetailsFragmentDoc } from './BalloonGFVSettingsDetails';
import { LeaseSettingsDetailsFragmentDoc } from './LeaseSettingsDetails';
import { DepositSettingsDetailsFragmentDoc } from './DepositSettingsDetails';
import { ResidualValueSettingsDetailsFragmentDoc } from './ResidualValueSettingsDetails';
import { LocalUcclLeasingOnlyDetailsFragmentDoc } from './LocalUcclLeasingOnlyDetails';
import { CounterSettingsSpecsFragmentDoc } from './CounterSettingsSpecs';
import { DealerFinanceProductsSpecsFragmentDoc } from './DealerFinanceProductsSpecs';
import { FinanceProductListDataFragmentDoc } from './FinanceProductListData';
import { DealerInsuranceProductsSpecsFragmentDoc } from './DealerInsuranceProductsSpecs';
import { InsuranceProductListDataFragmentDoc } from './InsuranceProductListData';
import { ErgoLookupTableSettingsDetailsFragmentDoc } from './ErgoLookupTableSettingDetails';
import { SalesOfferModuleEmailContentsSpecsFragmentDoc, SalesOfferEmailContentsSpecsFragmentDoc } from './SalesOfferModuleEmailContentsSpecs';
import { SalesOfferKycPresetSpecsFragmentDoc } from './SalesOfferKYCPresetSpecs';
import { SalesOfferConsentsSpecsFragmentDoc } from './SalesOfferConsentsSpecs';
import { SalesOfferSigningsSpecsFragmentDoc } from './SalesOfferSigningsSpecs';
export type EventApplicationSpecFragment = (
  { __typename: 'EventApplication' }
  & Pick<SchemaTypes.EventApplication, 'dealerId' | 'withCustomerDevice' | 'expireAt' | 'eventId' | 'bankId' | 'testDriveRedirectUrl' | 'journeySteps' | 'urlSlug'>
  & { applicantAgreements: Array<(
    { __typename: 'CheckboxApplicationAgreement' }
    & ApplicationAgreementData_CheckboxApplicationAgreement_Fragment
  ) | (
    { __typename: 'GroupApplicationAgreement' }
    & ApplicationAgreementData_GroupApplicationAgreement_Fragment
  ) | (
    { __typename: 'MarketingApplicationAgreement' }
    & ApplicationAgreementData_MarketingApplicationAgreement_Fragment
  ) | (
    { __typename: 'TextApplicationAgreement' }
    & ApplicationAgreementData_TextApplicationAgreement_Fragment
  )>, availableAssignees: Array<(
    { __typename: 'User' }
    & UsersOptionsDataFragment
  )>, dealer: (
    { __typename: 'Dealer' }
    & DealerJourneyDataFragment
  ), deposit?: SchemaTypes.Maybe<(
    { __typename: 'ApplicationAdyenDeposit' }
    & ApplicationJourneyDeposit_ApplicationAdyenDeposit_Fragment
  ) | (
    { __typename: 'ApplicationFiservDeposit' }
    & ApplicationJourneyDeposit_ApplicationFiservDeposit_Fragment
  ) | (
    { __typename: 'ApplicationPayGateDeposit' }
    & ApplicationJourneyDeposit_ApplicationPayGateDeposit_Fragment
  ) | (
    { __typename: 'ApplicationPorscheDeposit' }
    & ApplicationJourneyDeposit_ApplicationPorscheDeposit_Fragment
  ) | (
    { __typename: 'ApplicationTtbDeposit' }
    & ApplicationJourneyDeposit_ApplicationTtbDeposit_Fragment
  )>, draftFlow: (
    { __typename: 'StandardApplicationDraftFlow' }
    & JourneyDraftFlowFragment
  ), documents: Array<(
    { __typename: 'ApplicationDocument' }
    & ApplicationDocumentDataFragment
  )>, applicant: (
    { __typename: 'CorporateCustomer' }
    & CustomerSpecs_CorporateCustomer_Fragment
  ) | (
    { __typename: 'Guarantor' }
    & CustomerSpecs_Guarantor_Fragment
  ) | (
    { __typename: 'LocalCustomer' }
    & CustomerSpecs_LocalCustomer_Fragment
  ), guarantor?: SchemaTypes.Maybe<(
    { __typename: 'CorporateCustomer' }
    & CustomerSpecs_CorporateCustomer_Fragment
  ) | (
    { __typename: 'Guarantor' }
    & CustomerSpecs_Guarantor_Fragment
  ) | (
    { __typename: 'LocalCustomer' }
    & CustomerSpecs_LocalCustomer_Fragment
  )>, applicantKYC: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )>, testDriveKYC: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )>, testDriveAgreements: Array<(
    { __typename: 'CheckboxApplicationAgreement' }
    & ApplicationAgreementData_CheckboxApplicationAgreement_Fragment
  ) | (
    { __typename: 'GroupApplicationAgreement' }
    & ApplicationAgreementData_GroupApplicationAgreement_Fragment
  ) | (
    { __typename: 'MarketingApplicationAgreement' }
    & ApplicationAgreementData_MarketingApplicationAgreement_Fragment
  ) | (
    { __typename: 'TextApplicationAgreement' }
    & ApplicationAgreementData_TextApplicationAgreement_Fragment
  )>, showroomVisitAgreements: Array<(
    { __typename: 'CheckboxApplicationAgreement' }
    & ApplicationAgreementData_CheckboxApplicationAgreement_Fragment
  ) | (
    { __typename: 'GroupApplicationAgreement' }
    & ApplicationAgreementData_GroupApplicationAgreement_Fragment
  ) | (
    { __typename: 'MarketingApplicationAgreement' }
    & ApplicationAgreementData_MarketingApplicationAgreement_Fragment
  ) | (
    { __typename: 'TextApplicationAgreement' }
    & ApplicationAgreementData_TextApplicationAgreement_Fragment
  )>, guarantorAgreements: Array<(
    { __typename: 'CheckboxApplicationAgreement' }
    & ApplicationAgreementData_CheckboxApplicationAgreement_Fragment
  ) | (
    { __typename: 'GroupApplicationAgreement' }
    & ApplicationAgreementData_GroupApplicationAgreement_Fragment
  ) | (
    { __typename: 'MarketingApplicationAgreement' }
    & ApplicationAgreementData_MarketingApplicationAgreement_Fragment
  ) | (
    { __typename: 'TextApplicationAgreement' }
    & ApplicationAgreementData_TextApplicationAgreement_Fragment
  )>, guarantorKYC: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )>, corporateAgreements: Array<(
    { __typename: 'CheckboxApplicationAgreement' }
    & ApplicationAgreementData_CheckboxApplicationAgreement_Fragment
  ) | (
    { __typename: 'GroupApplicationAgreement' }
    & ApplicationAgreementData_GroupApplicationAgreement_Fragment
  ) | (
    { __typename: 'MarketingApplicationAgreement' }
    & ApplicationAgreementData_MarketingApplicationAgreement_Fragment
  ) | (
    { __typename: 'TextApplicationAgreement' }
    & ApplicationAgreementData_TextApplicationAgreement_Fragment
  )>, corporateKYC: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )>, configuration: (
    { __typename: 'EventApplicationConfiguration' }
    & Pick<SchemaTypes.EventApplicationConfiguration, 'assetCondition' | 'tradeIn' | 'testDrive' | 'visitAppointment'>
  ), vehicle?: SchemaTypes.Maybe<{ __typename: 'FinderVehicle' } | { __typename: 'LocalMake' } | { __typename: 'LocalModel' } | (
    { __typename: 'LocalVariant' }
    & ApplicationVariantSpecFragment
  )>, event: (
    { __typename: 'Event' }
    & JourneyEventDataFragment
  ), tradeInVehicle: Array<(
    { __typename: 'TradeInVehicle' }
    & TradeInVehicleDataFragment
  )>, signing?: SchemaTypes.Maybe<(
    { __typename: 'ApplicationNamirialSigning' }
    & NamirialSigningDataFragment
  ) | { __typename: 'ApplicationOTPSigning' }>, guarantorSigning?: SchemaTypes.Maybe<(
    { __typename: 'ApplicationNamirialSigning' }
    & NamirialSigningDataFragment
  ) | { __typename: 'ApplicationOTPSigning' }>, testDriveSigning?: SchemaTypes.Maybe<(
    { __typename: 'ApplicationNamirialSigning' }
    & NamirialSigningDataFragment
  ) | { __typename: 'ApplicationOTPSigning' }>, module: { __typename: 'AdyenPaymentModule' } | { __typename: 'AppointmentModule' } | { __typename: 'AutoplayModule' } | { __typename: 'BankModule' } | { __typename: 'BasicSigningModule' } | { __typename: 'CapModule' } | { __typename: 'ConfiguratorModule' } | { __typename: 'ConsentsAndDeclarationsModule' } | { __typename: 'CtsModule' } | { __typename: 'DocusignModule' } | (
    { __typename: 'EventApplicationModule' }
    & EventApplicationModuleDebugJourneyFragment
  ) | { __typename: 'FinderApplicationPrivateModule' } | { __typename: 'FinderApplicationPublicModule' } | { __typename: 'FinderVehicleManagementModule' } | { __typename: 'FiservPaymentModule' } | { __typename: 'GiftVoucherModule' } | { __typename: 'InsuranceModule' } | { __typename: 'LabelsModule' } | { __typename: 'LaunchPadModule' } | { __typename: 'LocalCustomerManagementModule' } | { __typename: 'MaintenanceModule' } | { __typename: 'MarketingModule' } | { __typename: 'MobilityModule' } | { __typename: 'MyInfoModule' } | { __typename: 'NamirialSigningModule' } | { __typename: 'OFRModule' } | { __typename: 'OIDCModule' } | { __typename: 'PayGatePaymentModule' } | { __typename: 'PorscheIdModule' } | { __typename: 'PorscheMasterDataModule' } | { __typename: 'PorschePaymentModule' } | { __typename: 'PorscheRetainModule' } | { __typename: 'PromoCodeModule' } | { __typename: 'SalesControlBoardModule' } | { __typename: 'SalesOfferModule' } | { __typename: 'SimpleVehicleManagementModule' } | { __typename: 'StandardApplicationModule' } | { __typename: 'TradeInModule' } | { __typename: 'TtbPaymentModule' } | { __typename: 'UserlikeChatbotModule' } | { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' } | { __typename: 'VisitAppointmentModule' } | { __typename: 'WebsiteModule' } | { __typename: 'WhatsappLiveChatModule' }, bank?: SchemaTypes.Maybe<(
    { __typename: 'SystemBank' }
    & Pick<SchemaTypes.SystemBank, 'id' | 'remoteFlowAcknowledgmentInfo' | 'hasUploadDocuments' | 'showCommentsField'>
  )>, customizedFields: Array<(
    { __typename: 'EventCustomizedField' }
    & ApplicationEventCustomizedFieldDataFragment
  )>, router?: SchemaTypes.Maybe<(
    { __typename: 'Router' }
    & Pick<SchemaTypes.Router, 'id' | 'pathname' | 'hostname'>
  )>, endpoint?: SchemaTypes.Maybe<(
    { __typename: 'ApplicationListEndpoint' }
    & Pick<SchemaTypes.ApplicationListEndpoint, 'id' | 'pathname'>
  ) | (
    { __typename: 'ConfiguratorApplicationEntrypoint' }
    & Pick<SchemaTypes.ConfiguratorApplicationEntrypoint, 'id' | 'pathname'>
  ) | (
    { __typename: 'CustomerListEndpoint' }
    & Pick<SchemaTypes.CustomerListEndpoint, 'id' | 'pathname'>
  ) | (
    { __typename: 'DummyPrivatePageEndpoint' }
    & Pick<SchemaTypes.DummyPrivatePageEndpoint, 'id' | 'pathname'>
  ) | (
    { __typename: 'DummyWelcomePageEndpoint' }
    & Pick<SchemaTypes.DummyWelcomePageEndpoint, 'id' | 'pathname'>
  ) | (
    { __typename: 'EventApplicationEntrypoint' }
    & Pick<SchemaTypes.EventApplicationEntrypoint, 'id' | 'pathname'>
  ) | (
    { __typename: 'FinderApplicationEntrypoint' }
    & Pick<SchemaTypes.FinderApplicationEntrypoint, 'id' | 'pathname'>
  ) | (
    { __typename: 'FinderApplicationPublicAccessEntrypoint' }
    & Pick<SchemaTypes.FinderApplicationPublicAccessEntrypoint, 'id' | 'pathname'>
  ) | (
    { __typename: 'LaunchPadApplicationEntrypoint' }
    & Pick<SchemaTypes.LaunchPadApplicationEntrypoint, 'id' | 'pathname'>
  ) | (
    { __typename: 'LeadListEndpoint' }
    & Pick<SchemaTypes.LeadListEndpoint, 'id' | 'pathname'>
  ) | (
    { __typename: 'MobilityApplicationEntrypoint' }
    & Pick<SchemaTypes.MobilityApplicationEntrypoint, 'id' | 'pathname'>
  ) | (
    { __typename: 'StandardApplicationEntrypoint' }
    & Pick<SchemaTypes.StandardApplicationEntrypoint, 'id' | 'pathname'>
  ) | (
    { __typename: 'StandardApplicationPublicAccessEntrypoint' }
    & Pick<SchemaTypes.StandardApplicationPublicAccessEntrypoint, 'id' | 'pathname'>
  ) | (
    { __typename: 'WebPageEndpoint' }
    & Pick<SchemaTypes.WebPageEndpoint, 'id' | 'pathname'>
  )>, lead: (
    { __typename: 'ConfiguratorLead' }
    & LeadData_ConfiguratorLead_Fragment
  ) | (
    { __typename: 'EventLead' }
    & LeadData_EventLead_Fragment
  ) | (
    { __typename: 'FinderLead' }
    & LeadData_FinderLead_Fragment
  ) | (
    { __typename: 'LaunchpadLead' }
    & LeadData_LaunchpadLead_Fragment
  ) | (
    { __typename: 'MobilityLead' }
    & LeadData_MobilityLead_Fragment
  ) | (
    { __typename: 'StandardLead' }
    & LeadData_StandardLead_Fragment
  ) }
);

export const EventApplicationSpecFragmentDoc = /*#__PURE__*/ gql`
    fragment EventApplicationSpec on EventApplication {
  dealerId
  withCustomerDevice
  applicantAgreements {
    ...ApplicationAgreementData
  }
  availableAssignees {
    ...UsersOptionsData
  }
  dealer {
    ...DealerJourneyData
  }
  deposit {
    ...ApplicationJourneyDeposit
  }
  expireAt
  draftFlow {
    ...JourneyDraftFlow
  }
  documents {
    ...ApplicationDocumentData
  }
  applicant {
    ...CustomerSpecs
  }
  guarantor {
    ...CustomerSpecs
  }
  applicantKYC {
    ...KYCFieldSpecs
  }
  testDriveKYC {
    ...KYCFieldSpecs
  }
  testDriveAgreements {
    ...ApplicationAgreementData
  }
  showroomVisitAgreements {
    ...ApplicationAgreementData
  }
  guarantorAgreements {
    ...ApplicationAgreementData
  }
  guarantorKYC {
    ...KYCFieldSpecs
  }
  corporateAgreements {
    ...ApplicationAgreementData
  }
  corporateKYC {
    ...KYCFieldSpecs
  }
  configuration {
    assetCondition
    tradeIn
    testDrive
    visitAppointment
  }
  vehicle {
    ...ApplicationVariantSpec
  }
  eventId
  event {
    ...JourneyEventData
  }
  tradeInVehicle {
    ...TradeInVehicleData
  }
  signing {
    ... on ApplicationNamirialSigning {
      ...NamirialSigningData
    }
  }
  guarantorSigning {
    ... on ApplicationNamirialSigning {
      ...NamirialSigningData
    }
  }
  testDriveSigning {
    ... on ApplicationNamirialSigning {
      ...NamirialSigningData
    }
  }
  module {
    ... on EventApplicationModule {
      ...EventApplicationModuleDebugJourney
    }
  }
  bankId
  bank {
    id
    remoteFlowAcknowledgmentInfo
    hasUploadDocuments
    showCommentsField
  }
  customizedFields {
    ...ApplicationEventCustomizedFieldData
  }
  router {
    id
    pathname
    hostname
  }
  endpoint {
    id
    pathname
  }
  testDriveRedirectUrl
  journeySteps
  urlSlug
  lead {
    ...LeadData
  }
}
    `;